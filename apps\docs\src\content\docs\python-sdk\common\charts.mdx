---
title: "Charts"
hideTitleOnPage: true
---

## Chart

```python
class Chart()
```

Represents a chart with metadata from matplotlib.

**Attributes**:

- `type` _ChartType_ - The type of chart
- `title` _str_ - The title of the chart
- `elements` _List[Any]_ - The elements of the chart
- `png` _Optional[str]_ - The PNG representation of the chart encoded in base64


## ChartType

```python
class ChartType(str, Enum)
```

Chart types

**Enum Members**:
    - `LINE` ("line")
    - `SCATTER` ("scatter")
    - `BAR` ("bar")
    - `PIE` ("pie")
    - `BOX_AND_WHISKER` ("box_and_whisker")
    - `COMPOSITE_CHART` ("composite_chart")
    - `UNKNOWN` ("unknown")

## Chart2D

```python
class Chart2D(Chart)
```

Represents a 2D chart with metadata.

**Attributes**:

- `x_label` _Optional[str]_ - The label of the x-axis
- `y_label` _Optional[str]_ - The label of the y-axis

## PointData

```python
class PointData()
```

Represents a point in a 2D chart.

**Attributes**:

- `label` _str_ - The label of the point
- `points` _List[Tuple[Union[str, float], Union[str, float]]]_ - The points of the chart

## PointChart

```python
class PointChart(Chart2D)
```

Represents a point chart with metadata.

**Attributes**:

- `x_ticks` _List[Union[str, float]]_ - The ticks of the x-axis
- `x_tick_labels` _List[str]_ - The labels of the x-axis
- `x_scale` _str_ - The scale of the x-axis
- `y_ticks` _List[Union[str, float]]_ - The ticks of the y-axis
- `y_tick_labels` _List[str]_ - The labels of the y-axis
- `y_scale` _str_ - The scale of the y-axis
- `elements` _List[PointData]_ - The points of the chart

## LineChart

```python
class LineChart(PointChart)
```

Represents a line chart with metadata.

**Attributes**:

- `type` _ChartType_ - The type of chart

## ScatterChart

```python
class ScatterChart(PointChart)
```

Represents a scatter chart with metadata.

**Attributes**:

- `type` _ChartType_ - The type of chart

## BarData

```python
class BarData()
```

Represents a bar in a bar chart.

**Attributes**:

- `label` _str_ - The label of the bar
- `group` _str_ - The group of the bar
- `value` _str_ - The value of the bar

## BarChart

```python
class BarChart(Chart2D)
```

Represents a bar chart with metadata.

**Attributes**:

- `type` _ChartType_ - The type of chart
- `elements` _List[BarData]_ - The bars of the chart

## PieData

```python
class PieData()
```

Represents a pie slice in a pie chart.

**Attributes**:

- `label` _str_ - The label of the pie slice
- `angle` _float_ - The angle of the pie slice
- `radius` _float_ - The radius of the pie slice
- `autopct` _float_ - The autopct value of the pie slice

## PieChart

```python
class PieChart(Chart)
```

Represents a pie chart with metadata.

**Attributes**:

- `type` _ChartType_ - The type of chart
- `elements` _List[PieData]_ - The pie slices of the chart

## BoxAndWhiskerData

```python
class BoxAndWhiskerData()
```

Represents a box and whisker in a box and whisker chart.

**Attributes**:

- `label` _str_ - The label of the box and whisker
- `min` _float_ - The minimum value of the box and whisker
- `first_quartile` _float_ - The first quartile of the box and whisker
- `median` _float_ - The median of the box and whisker
- `third_quartile` _float_ - The third quartile of the box and whisker
- `max` _float_ - The maximum value of the box and whisker
- `outliers` _List[float]_ - The outliers of the box and whisker

## BoxAndWhiskerChart

```python
class BoxAndWhiskerChart(Chart2D)
```

Represents a box and whisker chart with metadata.

**Attributes**:

- `type` _ChartType_ - The type of chart
- `elements` _List[BoxAndWhiskerData]_ - The box and whiskers of the chart

## CompositeChart

```python
class CompositeChart(Chart)
```

Represents a composite chart with metadata. A composite chart is a chart
that contains multiple charts (subplots).

**Attributes**:

- `type` _ChartType_ - The type of chart
- `elements` _List[Chart]_ - The charts (subplots) of the composite chart

