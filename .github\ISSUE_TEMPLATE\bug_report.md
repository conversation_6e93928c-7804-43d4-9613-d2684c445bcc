---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:

1. Use these parameters '...'
2. Click on '...'
3. Execute the following command '...'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (required fields):**

- Deployment: [Production/OSS/Staging]
- Daytona client: [e.g. Python SDK v0.0.1 or Daytona CLI v0.0.1]

**Additional context**
Add any other context about the problem here.
