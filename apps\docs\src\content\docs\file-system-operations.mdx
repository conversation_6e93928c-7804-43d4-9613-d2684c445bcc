---
title: File System Operations
---

import { TabItem, Tabs } from '@astrojs/starlight/components'

The Daytona SDK provides comprehensive file system operations through the `fs` module in Sandboxes. This guide covers all available file system operations and best practices.

## Basic Operations

Daytona SDK provides an option to interact with the file system in Sandboxes. You can perform various operations like listing files, creating directories, reading and writing files, and more.

For simplicity, file operations by default assume you are operating in the root directory of the Sandbox user - e.g. `workspace` implies `/home/<USER>/workspace`, but you are free to provide an absolute workdir path as well - which should start with a leading slash `/`.

### Listing Files and Directories

Daytona SDK provides an option to list files and directories in a Sandbox using Python and TypeScript.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# List files in a directory
files = sandbox.fs.list_files("workspace")

for file in files:
print(f"Name: {file.name}")
print(f"Is directory: {file.is_dir}")
print(f"Size: {file.size}")
print(f"Modified: {file.mod_time}")

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// List files in a directory
const files = await sandbox.fs.listFiles("workspace")

files.forEach(file => {
    console.log(`Name: ${file.name}`)
    console.log(`Is directory: ${file.isDir}`)
    console.log(`Size: ${file.size}`)
    console.log(`Modified: ${file.modTime}`)
})
```

</TabItem>
</Tabs>

### Creating Directories

Daytona SDK provides an option to create directories with specific permissions using Python and TypeScript.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# Create with specific permissions
sandbox.fs.create_folder("workspace/new-dir", "755")
```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// Create with specific permissions
await sandbox.fs.createFolder("workspace/new-dir", "755")
```

</TabItem>
</Tabs>

### Uploading Files

Daytona SDK provides options to read, write, upload, download, and delete files in Sandboxes using Python and TypeScript.

#### Uploading a Single File

If you want to upload a single file, you can do it as follows:

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# Upload a single file
with open("local_file.txt", "rb") as f:
    content = f.read()
sandbox.fs.upload_file(content, "remote_file.txt")
```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// Upload a single file
const fileContent = Buffer.from('Hello, World!')
await sandbox.fs.uploadFile(fileContent, "data.txt")
```

</TabItem>
</Tabs>

#### Uploading Multiple Files

The following example shows how to efficiently upload multiple files with a single method call.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# Upload multiple files at once
files_to_upload = []

with open("file1.txt", "rb") as f1:
files_to_upload.append(FileUpload(
source=f1.read(),
destination="data/file1.txt",
))

with open("file2.txt", "rb") as f2:
files_to_upload.append(FileUpload(
source=f2.read(),
destination="data/file2.txt",
))

with open("settings.json", "rb") as f3:
files_to_upload.append(FileUpload(
source=f3.read(),
destination="config/settings.json",
))

sandbox.fs.upload_files(files_to_upload)

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// Upload multiple files at once
const files = [
    {
        source: Buffer.from('Content of file 1'),
        destination: 'data/file1.txt',
    },
    {
        source: Buffer.from('Content of file 2'),
        destination: 'data/file2.txt',
    },
    {
        source: Buffer.from('{"key": "value"}'),
        destination: 'config/settings.json',
    }
]

await sandbox.fs.uploadFiles(files)
```

</TabItem>
</Tabs>

### Downloading Files

The following commands downloads the file `file1.txt` from the Sandbox user's root directory and prints out the content:

<Tabs>
<TabItem label="Python" icon="seti:python">
```python

content = sandbox.fs.download_file("file1.txt")

with open("local_file.txt", "wb") as f:
f.write(content)

print(content.decode('utf-8'))

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript

const downloadedFile = await sandbox.fs.downloadFile("file1.txt")

console.log('File content:', downloadedFile.toString())

```

</TabItem>
</Tabs>

### Deleting files

Once you no longer need them, simply delete files by using the `delete_file` function.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python

sandbox.fs.delete_file("workspace/file.txt")

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript

await sandbox.fs.deleteFile("workspace/file.txt")
```

</TabItem>
</Tabs>

## Advanced Operations

Daytona SDK provides advanced file system operations like file permissions, search and replace, and more.

### File Permissions

Daytona SDK provides an option to set file permissions, get file permissions, and set directory permissions recursively using Python and TypeScript.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# Set file permissions
sandbox.fs.set_file_permissions("workspace/file.txt", "644")

# Get file permissions

file_info = sandbox.fs.get_file_info("workspace/file.txt")
print(f"Permissions: {file_info.permissions}")

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// Set file permissions
await sandbox.fs.setFilePermissions("workspace/file.txt", { mode: "644" })

// Get file permissions
const fileInfo = await sandbox.fs.getFileDetails("workspace/file.txt")
console.log(`Permissions: ${fileInfo.permissions}`)
```

</TabItem>
</Tabs>

### File Search and Replace

Daytona SDK provides an option to search for text in files and replace text in files using Python and TypeScript.

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
# Search for text in files; if a folder is specified, the search is recursive
results = sandbox.fs.find_files(
    path="workspace/src",
    pattern="text-of-interest"
)
for match in results:
    print(f"Absolute file path: {match.file}")
    print(f"Line number: {match.line}")
    print(f"Line content: {match.content}")
    print("\n")

# Replace text in files

sandbox.fs.replace_in_files(
files=["workspace/file1.txt", "workspace/file2.txt"],
pattern="old_text",
new_value="new_text"
)

```
</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
// Search for text in files; if a folder is specified, the search is recursive
const results = await sandbox.fs.findFiles({
    path="workspace/src",
    pattern: "text-of-interest"
})
results.forEach(match => {
    console.log('Absolute file path:', match.file)
    console.log('Line number:', match.line)
    console.log('Line content:', match.content)
})

// Replace text in files
await sandbox.fs.replaceInFiles(
    ["workspace/file1.txt", "workspace/file2.txt"],
    "old_text",
    "new_text"
)
```

</TabItem>
</Tabs>
