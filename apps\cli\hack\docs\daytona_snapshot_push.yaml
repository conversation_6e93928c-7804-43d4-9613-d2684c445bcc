name: daytona snapshot push
synopsis: Push local snapshot
description: |
  Push a local Docker image to Daytona. To securely build it on our infrastructure, use 'daytona snapshot build'
usage: daytona snapshot push [SNAPSHOT] [flags]
options:
  - name: cpu
    default_value: '0'
    usage: |
      CPU cores that will be allocated to the underlying sandboxes (default: 1)
  - name: disk
    default_value: '0'
    usage: |
      Disk space that will be allocated to the underlying sandboxes in GB (default: 3)
  - name: entrypoint
    shorthand: e
    usage: The entrypoint command for the image
  - name: memory
    default_value: '0'
    usage: |
      Memory that will be allocated to the underlying sandboxes in GB (default: 1)
  - name: name
    shorthand: 'n'
    usage: Specify the Snapshot name
inherited_options:
  - name: help
    default_value: 'false'
    usage: help for daytona
see_also:
  - daytona snapshot - Manage Daytona snapshots
