## daytona snapshot list

List all snapshots

### Synopsis

List all available Daytona snapshots

```
daytona snapshot list [flags]
```

### Options

```
  -f, --format string   Output format. Must be one of (yaml, json)
  -l, --limit int       Maximum number of items per page (default 100)
  -p, --page int        Page number for pagination (starting from 1) (default 1)
```

### Options inherited from parent commands

```
      --help   help for daytona
```

### SEE ALSO

- [daytona snapshot](daytona_snapshot.md) - Manage Daytona snapshots
