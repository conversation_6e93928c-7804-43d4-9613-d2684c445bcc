<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="728px" height="220px" viewBox="-0.5 -0.5 728 220" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36&quot; version=&quot;27.2.0&quot; scale=&quot;1&quot; border=&quot;0&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;bhnGPeD7QPbymYHpg9xH&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1426&quot; dy=&quot;766&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;850&quot; pageHeight=&quot;290&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-13&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-2&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-4&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;177&quot; y=&quot;42&quot; /&gt;&#10;              &lt;mxPoint x=&quot;827&quot; y=&quot;42&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-17&quot; value=&quot;sandbox.delete()&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;&quot; parent=&quot;Bm4ui8PYQ9D95VNtlp4L-13&quot; connectable=&quot;0&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.0598&quot; y=&quot;1&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-2&quot; value=&quot;STARTED&quot; style=&quot;whiteSpace=wrap;strokeWidth=2;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;strokeColor=#82b366;fontColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;148&quot; y=&quot;134&quot; width=&quot;116&quot; height=&quot;54&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-15&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-4&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;425&quot; y=&quot;142.0000000000001&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;766&quot; y=&quot;138.40999999999997&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;425&quot; y=&quot;72&quot; /&gt;&#10;              &lt;mxPoint x=&quot;827&quot; y=&quot;72&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-16&quot; value=&quot;sandbox.delete()&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;&quot; parent=&quot;Bm4ui8PYQ9D95VNtlp4L-15&quot; connectable=&quot;0&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.0334&quot; y=&quot;2&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; value=&quot;STOPPED&quot; style=&quot;whiteSpace=wrap;strokeWidth=2;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;fontStyle=0;strokeColor=#82b366;fontColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;366&quot; y=&quot;132&quot; width=&quot;118&quot; height=&quot;54&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-4&quot; value=&quot;DELETED&quot; style=&quot;rounded=1;whiteSpace=wrap;arcSize=50;strokeWidth=2;shape=ellipse;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;perimeter=ellipsePerimeter;fillColor=#f5f5f5;fontColor=#000000;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;130.41&quot; width=&quot;94&quot; height=&quot;61.18&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-11&quot; value=&quot;d&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;680&quot; y=&quot;134&quot; /&gt;&#10;              &lt;mxPoint x=&quot;680&quot; y=&quot;102&quot; /&gt;&#10;              &lt;mxPoint x=&quot;827&quot; y=&quot;102&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint x=&quot;681&quot; y=&quot;134&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;826.9999999999998&quot; y=&quot;130.40999999999997&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Bm4ui8PYQ9D95VNtlp4L-12&quot; value=&quot;sandbox.delete()&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;&quot; parent=&quot;Bm4ui8PYQ9D95VNtlp4L-11&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.0267&quot; y=&quot;1&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-6&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-5&quot; value=&quot;ARCHIVED&quot; style=&quot;whiteSpace=wrap;strokeWidth=2;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;strokeColor=#82b366;fontColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;134&quot; width=&quot;122&quot; height=&quot;54&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-7&quot; value=&quot;sandbox.stop()&quot; style=&quot;startArrow=none;endArrow=block;exitX=0.5;exitY=0;entryX=0.25;entryY=0;rounded=0;exitDx=0;exitDy=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-2&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;206&quot; y=&quot;102&quot; /&gt;&#10;              &lt;mxPoint x=&quot;396&quot; y=&quot;102&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-8&quot; value=&quot;auto-stop&quot; style=&quot;startArrow=none;endArrow=block;exitX=1;exitY=0.5;entryX=0;entryY=0.5;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontSize=14;fontColor=#595959;fontStyle=2&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-2&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;126.04265306122446&quot; y=&quot;208&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;332.09627450980395&quot; y=&quot;152.00000000000003&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-10&quot; value=&quot;sandbox.start()&quot; style=&quot;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=1;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontSize=14;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#595959;&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-2&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.0021&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;425&quot; y=&quot;212&quot; /&gt;&#10;              &lt;mxPoint x=&quot;206&quot; y=&quot;212&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-11&quot; value=&quot;sandbox.archive()&quot; style=&quot;startArrow=none;endArrow=block;exitX=0.75;exitY=0;entryX=0.28;entryY=-0.01;rounded=0;edgeStyle=orthogonalEdgeStyle;exitDx=0;exitDy=0;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-5&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;455&quot; y=&quot;102&quot; /&gt;&#10;              &lt;mxPoint x=&quot;655&quot; y=&quot;102&quot; /&gt;&#10;              &lt;mxPoint x=&quot;655&quot; y=&quot;120&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-12&quot; value=&quot;auto-archive&quot; style=&quot;startArrow=none;endArrow=block;exitX=1;exitY=0.5;entryX=0;entryY=0.5;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontStyle=2;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; source=&quot;ZXRL-tFffRt2OgFoSzfc-3&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-5&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.1304&quot; y=&quot;1&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;ZXRL-tFffRt2OgFoSzfc-14&quot; value=&quot;sandbox.start()&quot; style=&quot;startArrow=none;endArrow=block;entryX=0.25;entryY=1;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;673&quot; y=&quot;188&quot; /&gt;&#10;              &lt;mxPoint x=&quot;673&quot; y=&quot;244&quot; /&gt;&#10;              &lt;mxPoint x=&quot;190&quot; y=&quot;244&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint x=&quot;673&quot; y=&quot;188&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;190.00000000000006&quot; y=&quot;190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;L9USphHFUnbPNhMHQVU4-6&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; target=&quot;ZXRL-tFffRt2OgFoSzfc-4&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;500&quot; y=&quot;160&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;830&quot; y=&quot;230&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;500&quot; y=&quot;190&quot; /&gt;&#10;              &lt;mxPoint x=&quot;500&quot; y=&quot;210&quot; /&gt;&#10;              &lt;mxPoint x=&quot;827&quot; y=&quot;210&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;L9USphHFUnbPNhMHQVU4-9&quot; value=&quot;&amp;lt;span style=&amp;quot;color: rgb(89, 89, 89); font-family: Helvetica; font-size: 14px; font-style: italic; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&amp;quot;&amp;gt;auto-delete&amp;lt;/span&amp;gt;&quot; style=&quot;text;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;550&quot; y=&quot;191.59&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-13"><g><path d="M 30 101 L 30 9 L 680 9 L 680 91.04" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 680 96.29 L 676.5 89.29 L 680 91.04 L 683.5 89.29 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-17"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 9px; margin-left: 379px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #595959; background-color: #ffffff; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#595959, #a1a1a1); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">sandbox.delete()</div></div></div></foreignObject><text x="379" y="13" fill="#595959" font-family="Helvetica" font-size="14px" text-anchor="middle">sandbox.delete()</text></switch></g></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-2"><g><rect x="1" y="101" width="116" height="54" rx="8.1" ry="8.1" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 114px; height: 1px; padding-top: 128px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">STARTED</div></div></div></foreignObject><text x="59" y="132" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">STARTED</text></switch></g></g></g><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-15"><g><path d="M 278 109 L 278 39 L 680 39 L 680 91.04" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 680 96.29 L 676.5 89.29 L 680 91.04 L 683.5 89.29 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-16"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 38px; margin-left: 483px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #595959; background-color: #ffffff; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#595959, #a1a1a1); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">sandbox.delete()</div></div></div></foreignObject><text x="483" y="42" fill="#595959" font-family="Helvetica" font-size="14px" text-anchor="middle">sandbox.delete()</text></switch></g></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-3"><g><rect x="219" y="99" width="118" height="54" rx="8.1" ry="8.1" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 116px; height: 1px; padding-top: 126px; margin-left: 220px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">STOPPED</div></div></div></foreignObject><text x="278" y="130" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">STOPPED</text></switch></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-4"><g><ellipse cx="680" cy="128" rx="47" ry="30.59" fill="#f5f5f5" stroke="#666666" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(245, 245, 245), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 92px; height: 1px; padding-top: 128px; margin-left: 634px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DELETED</div></div></div></foreignObject><text x="680" y="132" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">DELETED</text></switch></g></g></g><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-11"><g><path d="M 534 101 L 533 101 L 533 69 L 680 69 L 680 91.04" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 680 96.29 L 676.5 89.29 L 680 91.04 L 683.5 89.29 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 69px; margin-left: 604px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #595959; background-color: #ffffff; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#595959, #a1a1a1); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">d</div></div></div></foreignObject><text x="604" y="73" fill="#595959" font-family="Helvetica" font-size="14px" text-anchor="middle">d</text></switch></g></g><g data-cell-id="Bm4ui8PYQ9D95VNtlp4L-12"><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 69px; margin-left: 596px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #595959; background-color: #ffffff; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#595959, #a1a1a1); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">sandbox.delete()</div></div></div></foreignObject><text x="596" y="73" fill="#595959" font-family="Helvetica" font-size="14px" text-anchor="middle">sandbox.delete()</text></switch></g></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-5"><g><rect x="473" y="101" width="122" height="54" rx="8.1" ry="8.1" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 128px; margin-left: 474px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: light-dark(#000000, #ededed); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ARCHIVED</div></div></div></foreignObject><text x="534" y="132" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">ARCHIVED</text></switch></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-7"><g><path d="M 59 101 L 59 69 L 248.5 69 L 248.5 90.88" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 248.5 97.88 L 245 90.88 L 252 90.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#595959" font-family="Helvetica" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="106" y="61" width="95" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="153" y="75">sandbox.stop()</text></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-8"><g><path d="M 117 128 L 168 128 L 168 126 L 210.88 126" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 217.88 126 L 210.88 129.5 L 210.88 122.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#595959" font-family="Helvetica" font-style="italic" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="139" y="119" width="60" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="168" y="133">auto-stop</text></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-10"><g><path d="M 278 153 L 278 179 L 59 179 L 59 163.12" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/><path d="M 59 156.12 L 62.5 163.12 L 55.5 163.12 Z" fill="#666666" stroke="#666666" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(102, 102, 102), rgb(149, 149, 149)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/></g><g><g fill="#595959" font-family="Helvetica" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="122" y="171" width="95" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="169" y="185">sandbox.start()</text></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-11"><g><path d="M 307.5 99 L 307.5 69 L 508 69 L 508 87 L 507.87 92.88" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 507.71 99.88 L 504.37 92.81 L 511.37 92.96 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#595959" font-family="Helvetica" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="353" y="61" width="113" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="408.5" y="75">sandbox.archive()</text></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-12"><g><path d="M 337 126 L 405 126 L 405 128 L 464.88 128" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 471.88 128 L 464.88 131.5 L 464.88 124.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#595959" font-family="Helvetica" font-style="italic" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="374" y="119" width="79" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="413" y="133">auto-archive</text></g></g></g><g data-cell-id="ZXRL-tFffRt2OgFoSzfc-14"><g><path d="M 526 155 L 526 211 L 43 211 L 43 165.12" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 43 158.12 L 46.5 165.12 L 39.5 165.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#595959" font-family="Helvetica" text-anchor="middle" font-size="14px" style="fill: light-dark(rgb(89, 89, 89), rgb(161, 161, 161));"><rect fill="#ffffff" stroke="none" x="238" y="203" width="95" height="18" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="285" y="217">sandbox.start()</text></g></g></g><g data-cell-id="L9USphHFUnbPNhMHQVU4-6"><g><path d="M 353 127 L 353 157 L 353 177 L 680 177 L 680 164.96" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 680 159.71 L 683.5 166.71 L 680 164.96 L 676.5 166.71 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="L9USphHFUnbPNhMHQVU4-9"><g><rect x="403" y="158.59" width="100" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 166px; margin-left: 405px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><span style="color: light-dark(rgb(89, 89, 89), rgb(161, 161, 161)); font-family: Helvetica; font-size: 14px; font-style: italic; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18)); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">auto-delete</span></div></div></div></foreignObject><text x="405" y="178" fill="light-dark(#000000, #ffffff)" font-family="Helvetica" font-size="12px">auto-delete</text></switch></g></g></g></g></g></g></svg>