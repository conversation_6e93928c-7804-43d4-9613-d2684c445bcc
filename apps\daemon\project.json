{"name": "daemon", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/daemon", "tags": [], "targets": {"prepare": {"executor": "@nx-go/nx-go:serve", "options": {"cwd": ".", "main": "{projectRoot}/tools/xterm.go"}, "inputs": ["{projectRoot}/tools/xterm.go"], "outputs": ["{projectRoot}/pkg/terminal/static/*"], "configurations": {"production": {}}}, "build": {"executor": "@nx-go/nx-go:build", "options": {"main": "{projectRoot}/cmd/daemon/main.go", "outputPath": "dist/apps/daemon", "flags": ["-ldflags \"-X 'github.com/daytonaio/daemon/internal.Version=${npm_package_version}'\""]}, "configurations": {"production": {}}, "dependsOn": ["build-amd64"]}, "build-amd64": {"executor": "@nx-go/nx-go:build", "options": {"main": "{projectRoot}/cmd/daemon/main.go", "outputPath": "dist/apps/daemon-amd64", "env": {"GOARCH": "amd64", "GOOS": "linux"}, "flags": ["-ldflags \"-X 'github.com/daytonaio/daemon/internal.Version=${npm_package_version}'\""]}, "dependsOn": ["prepare"]}, "serve": {"executor": "@nx-go/nx-go:serve", "options": {"cmd": "gow", "cwd": ".", "main": "{projectRoot}/cmd/daemon/main.go"}, "configurations": {"production": {}}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd {projectRoot} && go fmt ./... && prettier --write \"**/*.{yaml,html}\""}}, "test": {"executor": "@nx-go/nx-go:test"}, "lint": {"executor": "@nx-go/nx-go:lint"}}}