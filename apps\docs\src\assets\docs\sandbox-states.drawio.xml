<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram name="Page-1" id="bhnGPeD7QPbymYHpg9xH">
    <mxGraphModel dx="1426" dy="766" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="290" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;" parent="1" source="ZXRL-tFffRt2OgFoSzfc-2" target="ZXRL-tFffRt2OgFoSzfc-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="177" y="42" />
              <mxPoint x="827" y="42" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-17" value="sandbox.delete()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;" parent="Bm4ui8PYQ9D95VNtlp4L-13" connectable="0" vertex="1">
          <mxGeometry x="0.0598" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-2" value="STARTED" style="whiteSpace=wrap;strokeWidth=2;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;strokeColor=#82b366;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="148" y="134" width="116" height="54" as="geometry" />
        </mxCell>
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;" parent="1" target="ZXRL-tFffRt2OgFoSzfc-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="425" y="142.0000000000001" as="sourcePoint" />
            <mxPoint x="766" y="138.40999999999997" as="targetPoint" />
            <Array as="points">
              <mxPoint x="425" y="72" />
              <mxPoint x="827" y="72" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-16" value="sandbox.delete()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;" parent="Bm4ui8PYQ9D95VNtlp4L-15" connectable="0" vertex="1">
          <mxGeometry x="0.0334" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-3" value="STOPPED" style="whiteSpace=wrap;strokeWidth=2;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;fontStyle=0;strokeColor=#82b366;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="366" y="132" width="118" height="54" as="geometry" />
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-4" value="DELETED" style="rounded=1;whiteSpace=wrap;arcSize=50;strokeWidth=2;shape=ellipse;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;perimeter=ellipsePerimeter;fillColor=#f5f5f5;fontColor=#000000;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="780" y="130.41" width="94" height="61.18" as="geometry" />
        </mxCell>
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-11" value="d" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="680" y="134" />
              <mxPoint x="680" y="102" />
              <mxPoint x="827" y="102" />
            </Array>
            <mxPoint x="681" y="134" as="sourcePoint" />
            <mxPoint x="826.9999999999998" y="130.40999999999997" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Bm4ui8PYQ9D95VNtlp4L-12" value="sandbox.delete()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontColor=#595959;" parent="Bm4ui8PYQ9D95VNtlp4L-11" vertex="1" connectable="0">
          <mxGeometry x="-0.0267" y="1" relative="1" as="geometry">
            <mxPoint x="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-5" value="ARCHIVED" style="whiteSpace=wrap;strokeWidth=2;rounded=1;html=1;fillColor=#d5e8d4;fontSize=14;strokeColor=#82b366;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="620" y="134" width="122" height="54" as="geometry" />
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-7" value="sandbox.stop()" style="startArrow=none;endArrow=block;exitX=0.5;exitY=0;entryX=0.25;entryY=0;rounded=0;exitDx=0;exitDy=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;fontSize=14;fontColor=#595959;" parent="1" source="ZXRL-tFffRt2OgFoSzfc-2" target="ZXRL-tFffRt2OgFoSzfc-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="206" y="102" />
              <mxPoint x="396" y="102" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-8" value="auto-stop" style="startArrow=none;endArrow=block;exitX=1;exitY=0.5;entryX=0;entryY=0.5;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontSize=14;fontColor=#595959;fontStyle=2" parent="1" source="ZXRL-tFffRt2OgFoSzfc-2" target="ZXRL-tFffRt2OgFoSzfc-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="126.04265306122446" y="208" as="sourcePoint" />
            <mxPoint x="332.09627450980395" y="152.00000000000003" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-10" value="sandbox.start()" style="startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=1;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontSize=14;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#595959;" parent="1" source="ZXRL-tFffRt2OgFoSzfc-3" target="ZXRL-tFffRt2OgFoSzfc-2" edge="1">
          <mxGeometry x="0.0021" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="425" y="212" />
              <mxPoint x="206" y="212" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-11" value="sandbox.archive()" style="startArrow=none;endArrow=block;exitX=0.75;exitY=0;entryX=0.28;entryY=-0.01;rounded=0;edgeStyle=orthogonalEdgeStyle;exitDx=0;exitDy=0;fontSize=14;fontColor=#595959;" parent="1" source="ZXRL-tFffRt2OgFoSzfc-3" target="ZXRL-tFffRt2OgFoSzfc-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="455" y="102" />
              <mxPoint x="655" y="102" />
              <mxPoint x="655" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-12" value="auto-archive" style="startArrow=none;endArrow=block;exitX=1;exitY=0.5;entryX=0;entryY=0.5;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;exitDx=0;exitDy=0;fontStyle=2;fontSize=14;fontColor=#595959;" parent="1" source="ZXRL-tFffRt2OgFoSzfc-3" target="ZXRL-tFffRt2OgFoSzfc-5" edge="1">
          <mxGeometry x="0.1304" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZXRL-tFffRt2OgFoSzfc-14" value="sandbox.start()" style="startArrow=none;endArrow=block;entryX=0.25;entryY=1;rounded=0;edgeStyle=orthogonalEdgeStyle;entryDx=0;entryDy=0;fontSize=14;fontColor=#595959;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="673" y="188" />
              <mxPoint x="673" y="244" />
              <mxPoint x="190" y="244" />
            </Array>
            <mxPoint x="673" y="188" as="sourcePoint" />
            <mxPoint x="190.00000000000006" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="L9USphHFUnbPNhMHQVU4-6" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" target="ZXRL-tFffRt2OgFoSzfc-4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="160" as="sourcePoint" />
            <mxPoint x="830" y="230" as="targetPoint" />
            <Array as="points">
              <mxPoint x="500" y="190" />
              <mxPoint x="500" y="210" />
              <mxPoint x="827" y="210" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="L9USphHFUnbPNhMHQVU4-9" value="&lt;span style=&quot;color: rgb(89, 89, 89); font-family: Helvetica; font-size: 14px; font-style: italic; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;auto-delete&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="550" y="191.59" width="100" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>