---
title: Volumes
---

import { TabItem, Tabs } from '@astrojs/starlight/components'
import Label from '@components/Label.astro'

Volumes are FUSE-based mounts on the Sandbox file system that enable file sharing between Sandboxes and instant access to existing files already present on the mounted volume.
Using Volumes the Sandboxes can instantly read from large files eliminating the need to upload them first to the Sandbox file system.
The volume data is stored on the S3 compatible object store. Multiple volumes can be mounted to a single Sandbox and each volume can be mounted on multiple Sandboxes simultaneously.

## Creating Volumes

In order to mount a volume to a Sandbox, one must be created.

<Tabs>
  <TabItem label="Python" icon="seti:python">
    ```bash
    volume = daytona.volume.get("my-volume", create=True)
    ```
  </TabItem>
  <TabItem label="TypeScript" icon="seti:typescript">
    ```bash
    const volume = await daytona.volume.get('my-volume', true)
    ```
  </TabItem>
</Tabs>

## Mounting Volumes to Sandboxes

Once a volume is created, it can be mounted to a Sandbox by specifying it in the `CreateSandboxFromSnapshotParams` object:

<Tabs>
<TabItem label="Python" icon="seti:python">
```python
import os
from daytona import CreateSandboxFromSnapshotParams, Daytona, VolumeMount

daytona = Daytona()

# Create a new volume or get an existing one

volume = daytona.volume.get("my-volume", create=True)

# Mount the volume to the sandbox

mount_dir_1 = "/home/<USER>/volume"

params = CreateSandboxFromSnapshotParams(
language="python",
volumes=[VolumeMount(volumeId=volume.id, mountPath=mount_dir_1)],
)
sandbox = daytona.create(params)

# When you're done with the sandbox, you can remove it

# The volume will persist even after the sandbox is removed

sandbox.delete()

```

</TabItem>
<TabItem label="TypeScript" icon="seti:typescript">
```typescript
import { Daytona } from '@daytonaio/sdk'
import path from 'path'

async function main() {
  const daytona = new Daytona()

  //  Create a new volume or get an existing one
  const volume = await daytona.volume.get('my-volume', true)

  // Mount the volume to the sandbox
  const mountDir1 = '/home/<USER>/volume'

  const sandbox1 = await daytona.create({
    language: 'typescript',
    volumes: [{ volumeId: volume.id, mountPath: mountDir1 }],
  })

  // When you're done with the sandbox, you can remove it
  // The volume will persist even after the sandbox is removed
  await daytona.delete(sandbox1)
}

main()

```

</TabItem>
</Tabs>

## Deleting Volumes

When a volume is no longer needed, it can be removed.

<Tabs>
  <TabItem label="Python" icon="seti:python">
    ```python
    volume = daytona.volume.get("my-volume", create=True)
    daytona.volume.delete(volume)
    ```
  </TabItem>
  <TabItem label="TypeScript" icon="seti:typescript">
    ```typescript
    const volume = await daytona.volume.get('my-volume', true)
    await daytona.volume.delete(volume)
    ```
  </TabItem>
</Tabs>

## Working with Volumes

Once mounted, you can read from and write to the volume just like any other directory in the Sandbox file system. Files written to the volume persist beyond the lifecycle of any individual Sandbox.

## Limitations

Since volumes are FUSE-based mounts, they can not be used for applications that require block storage access (like database tables).
Volumes are generally slower for both read and write operations compared to the local Sandbox file system.
