name: daytona
synopsis: Daytona CLI
description: Command line interface for Daytona Sandboxes
usage: daytona [flags]
options:
  - name: help
    default_value: 'false'
    usage: help for daytona
  - name: version
    shorthand: v
    default_value: 'false'
    usage: Display the version of Daytona
see_also:
  - daytona autocomplete - Adds a completion script for your shell environment
  - daytona docs - Opens the Daytona documentation in your default browser.
  - daytona login - Log in to Daytona
  - daytona logout - Logout from Daytona
  - daytona mcp - Manage Daytona MCP Server
  - daytona organization - Manage Daytona organizations
  - daytona sandbox - Manage Daytona sandboxes
  - daytona snapshot - Manage Daytona snapshots
  - daytona version - Print the version number
  - daytona volume - Manage Daytona volumes
