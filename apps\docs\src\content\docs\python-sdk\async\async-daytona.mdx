---
title: "AsyncDaytona"
hideTitleOnPage: true
---

## AsyncDaytona

```python
class AsyncDaytona()
```

Main class for interacting with the Daytona API.

This class provides asynchronous methods to create, manage, and interact with Daytona Sandboxes.
It can be initialized either with explicit configuration or using environment variables.

**Attributes**:

- `volume` _AsyncVolumeService_ - Service for managing volumes.
- `snapshot` _AsyncSnapshotService_ - Service for managing snapshots.
  

**Example**:

  Using environment variables:
```python
async with AsyncDaytona() as daytona:  # Uses DAYTONA_API_KEY, DAYTONA_API_URL
    sandbox = await daytona.create()
```
  
  Using explicit configuration:
```python
config = DaytonaConfig(
    api_key="your-api-key",
    api_url="https://your-api.com",
    target="us"
)
try:
    daytona = AsyncDaytona(config)
    sandbox = await daytona.create()
finally:
    await daytona.close()
```

#### AsyncDaytona.\_\_init\_\_

```python
def __init__(config: Optional[DaytonaConfig] = None)
```

Initializes Daytona instance with optional configuration.

If no config is provided, reads from environment variables:
- `DAYTONA_API_KEY`: Required API key for authentication
- `DAYTONA_API_URL`: Required api URL
- `DAYTONA_TARGET`: Optional target environment (defaults to 'us')

**Arguments**:

- `config` _Optional[DaytonaConfig]_ - Object containing api_key, api_url, and target.
  

**Raises**:

- `DaytonaError` - If API key is not provided either through config or environment variables
  

**Example**:

```python
from daytona import Daytona, DaytonaConfig
# Using environment variables
daytona1 = AsyncDaytona()
await daytona1.close()
# Using explicit configuration
config = DaytonaConfig(
    api_key="your-api-key",
    api_url="https://your-api.com",
    target="us"
)
daytona2 = AsyncDaytona(config)
await daytona2.close()
```

#### AsyncDaytona.\_\_aenter\_\_

```python
async def __aenter__()
```

Async context manager entry.

#### AsyncDaytona.\_\_aexit\_\_

```python
async def __aexit__(exc_type, exc_value, traceback)
```

Async context manager exit - ensures proper cleanup.

#### AsyncDaytona.close

```python
async def close()
```

Close the HTTP session and clean up resources.

This method should be called when you're done using the AsyncDaytona instance
to properly close the underlying HTTP session and avoid resource leaks.

**Example**:

```python
daytona = AsyncDaytona()
try:
    sandbox = await daytona.create()
    # ... use sandbox ...
finally:
    await daytona.close()
```
  
  Or better yet, use as async context manager:
```python
async with AsyncDaytona() as daytona:
    sandbox = await daytona.create()
    # ... use sandbox ...
# Automatically closed
```

#### AsyncDaytona.create

```python
@overload
async def create(params: Optional[CreateSandboxFromSnapshotParams] = None,
                 *,
                 timeout: Optional[float] = 60) -> AsyncSandbox
```

Creates Sandboxes from specified or default snapshot. You can specify various parameters,
including language, image, environment variables, and volumes.

**Arguments**:

- `params` _Optional[CreateSandboxFromSnapshotParams]_ - Parameters for Sandbox creation. If not provided,
  defaults to default Daytona snapshot and Python language.
- `timeout` _Optional[float]_ - Timeout (in seconds) for sandbox creation. 0 means no timeout.
  Default is 60 seconds.
  

**Returns**:

- `Sandbox` - The created Sandbox instance.
  

**Raises**:

- `DaytonaError` - If timeout, auto_stop_interval or auto_archive_interval is negative;
  If sandbox fails to start or times out
  

**Example**:

  Create a default Python Sandbox:
```python
sandbox = await daytona.create()
```
  
  Create a custom Sandbox:
```python
params = CreateSandboxFromSnapshotParams(
    language="python",
    snapshot="my-snapshot-id",
    env_vars={"DEBUG": "true"},
    auto_stop_interval=0,
    auto_archive_interval=60,
    auto_delete_interval=120
)
sandbox = await daytona.create(params, timeout=40)
```

#### AsyncDaytona.create

```python
@overload
async def create(
        params: Optional[CreateSandboxFromImageParams] = None,
        *,
        timeout: Optional[float] = 60,
        on_snapshot_create_logs: Callable[[str], None] = None) -> AsyncSandbox
```

Creates Sandboxes from specified image available on some registry or declarative Daytona Image.
You can specify various parameters, including resources, language, image, environment variables,
and volumes. Daytona creates snapshot from provided image and uses it to create Sandbox.

**Arguments**:

- `params` _Optional[CreateSandboxFromImageParams]_ - Parameters for Sandbox creation from image.
- `timeout` _Optional[float]_ - Timeout (in seconds) for sandbox creation. 0 means no timeout.
  Default is 60 seconds.
- `on_snapshot_create_logs` _Callable[[str], None]_ - This callback function handles snapshot creation logs.
  

**Returns**:

- `Sandbox` - The created Sandbox instance.
  

**Raises**:

- `DaytonaError` - If timeout, auto_stop_interval or auto_archive_interval is negative;
  If sandbox fails to start or times out
  

**Example**:

  Create a default Python Sandbox from image:
```python
sandbox = await daytona.create(CreateSandboxFromImageParams(image="debian:12.9"))
```
  
  Create a custom Sandbox from declarative Image definition:
```python
declarative_image = (
    Image.base("alpine:3.18")
    .pipInstall(["numpy", "pandas"])
    .env({"MY_ENV_VAR": "My Environment Variable"})
)
params = CreateSandboxFromImageParams(
    language="python",
    image=declarative_image,
    env_vars={"DEBUG": "true"},
    resources=Resources(cpu=2, memory=4),
    auto_stop_interval=0,
    auto_archive_interval=60,
    auto_delete_interval=120
)
sandbox = await daytona.create(
    params,
    timeout=40,
    on_snapshot_create_logs=lambda chunk: print(chunk, end=""),
)
```

#### AsyncDaytona.delete

```python
async def delete(sandbox: AsyncSandbox, timeout: Optional[float] = 60) -> None
```

Deletes a Sandbox.

**Arguments**:

- `sandbox` _Sandbox_ - The Sandbox instance to delete.
- `timeout` _Optional[float]_ - Timeout (in seconds) for sandbox deletion. 0 means no timeout.
  Default is 60 seconds.
  

**Raises**:

- `DaytonaError` - If sandbox fails to delete or times out
  

**Example**:

```python
sandbox = await daytona.create()
# ... use sandbox ...
await daytona.delete(sandbox)  # Clean up when done
```

#### AsyncDaytona.get

```python
@intercept_errors(message_prefix="Failed to get sandbox: ")
async def get(sandbox_id: str) -> AsyncSandbox
```

Gets a Sandbox by its ID.

**Arguments**:

- `sandbox_id` _str_ - The ID of the Sandbox to retrieve.
  

**Returns**:

- `Sandbox` - The Sandbox instance.
  

**Raises**:

- `DaytonaError` - If sandbox_id is not provided.
  

**Example**:

```python
sandbox = await daytona.get("my-sandbox-id")
print(sandbox.status)
```

#### AsyncDaytona.find\_one

```python
@intercept_errors(message_prefix="Failed to find sandbox: ")
async def find_one(sandbox_id: Optional[str] = None,
                   labels: Optional[Dict[str, str]] = None) -> AsyncSandbox
```

Finds a Sandbox by its ID or labels.

**Arguments**:

- `sandbox_id` _Optional[str]_ - The ID of the Sandbox to retrieve.
- `labels` _Optional[Dict[str, str]]_ - Labels to filter Sandboxes.
  

**Returns**:

- `Sandbox` - First Sandbox that matches the ID or labels.
  

**Raises**:

- `DaytonaError` - If no Sandbox is found.
  

**Example**:

```python
sandbox = await daytona.find_one(labels={"my-label": "my-value"})
print(f"Sandbox ID: {sandbox.id} State: {sandbox.state}")
```

#### AsyncDaytona.list

```python
@intercept_errors(message_prefix="Failed to list sandboxes: ")
async def list(labels: Optional[Dict[str, str]] = None) -> List[AsyncSandbox]
```

Lists Sandboxes filtered by labels.

**Arguments**:

- `labels` _Optional[Dict[str, str]]_ - Labels to filter Sandboxes.
  

**Returns**:

- `List[Sandbox]` - List of Sandbox instances that match the labels.
  

**Example**:

```python
sandboxes = await daytona.list(labels={"my-label": "my-value"})
for sandbox in sandboxes:
    print(f"{sandbox.id}: {sandbox.status}")
```

#### AsyncDaytona.start

```python
async def start(sandbox: AsyncSandbox, timeout: Optional[float] = 60) -> None
```

Starts a Sandbox and waits for it to be ready.

**Arguments**:

- `sandbox` _Sandbox_ - The Sandbox to start.
- `timeout` _Optional[float]_ - Optional timeout in seconds to wait for the Sandbox to start.
  0 means no timeout. Default is 60 seconds.
  

**Raises**:

- `DaytonaError` - If timeout is negative; If Sandbox fails to start or times out

#### AsyncDaytona.stop

```python
async def stop(sandbox: AsyncSandbox, timeout: Optional[float] = 60) -> None
```

Stops a Sandbox and waits for it to be stopped.

**Arguments**:

- `sandbox` _Sandbox_ - The sandbox to stop
- `timeout` _Optional[float]_ - Optional timeout (in seconds) for sandbox stop.
  0 means no timeout. Default is 60 seconds.
  

**Raises**:

- `DaytonaError` - If timeout is negative; If Sandbox fails to stop or times out


## CodeLanguage

```python
@dataclass
class CodeLanguage(Enum)
```

Programming languages supported by Daytona

**Enum Members**:
    - `PYTHON` ("python")
    - `TYPESCRIPT` ("typescript")
    - `JAVASCRIPT` ("javascript")

## DaytonaConfig

```python
class DaytonaConfig(BaseModel)
```

Configuration options for initializing the Daytona client.

**Attributes**:

- `api_key` _Optional[str]_ - API key for authentication with the Daytona API. If not set, it must be provided
  via the environment variable `DAYTONA_API_KEY`, or a JWT token must be provided instead.
- `jwt_token` _Optional[str]_ - JWT token for authentication with the Daytona API. If not set, it must be provided
  via the environment variable `DAYTONA_JWT_TOKEN`, or an API key must be provided instead.
- `organization_id` _Optional[str]_ - Organization ID used for JWT-based authentication. Required if a JWT token
  is provided, and must be set either here or in the environment variable `DAYTONA_ORGANIZATION_ID`.
- `api_url` _Optional[str]_ - URL of the Daytona API. Defaults to `'https://app.daytona.io/api'` if not set
  here or in the environment variable `DAYTONA_API_URL`.
- `server_url` _Optional[str]_ - Deprecated. Use `api_url` instead. This property will be removed
  in a future version.
- `target` _Optional[str]_ - Target runner location for the Sandbox. Defaults to `'us'` if not set here
  or in the environment variable `DAYTONA_TARGET`.
  

**Example**:

```python
config = DaytonaConfig(api_key="your-api-key")
```
```python
config = DaytonaConfig(jwt_token="your-jwt-token", organization_id="your-organization-id")
```

## CreateSandboxBaseParams

```python
class CreateSandboxBaseParams(BaseModel)
```

Base parameters for creating a new Sandbox.

**Attributes**:

- `language` _Optional[CodeLanguage]_ - Programming language for the Sandbox. Defaults to "python".
- `os_user` _Optional[str]_ - OS user for the Sandbox.
- `env_vars` _Optional[Dict[str, str]]_ - Environment variables to set in the Sandbox.
- `labels` _Optional[Dict[str, str]]_ - Custom labels for the Sandbox.
- `public` _Optional[bool]_ - Whether the Sandbox should be public.
- `timeout` _Optional[float]_ - Timeout in seconds for Sandbox to be created and started.
- `auto_stop_interval` _Optional[int]_ - Interval in minutes after which Sandbox will
  automatically stop if no Sandbox event occurs during that time. Default is 15 minutes.
  0 means no auto-stop.
- `auto_archive_interval` _Optional[int]_ - Interval in minutes after which a continuously stopped Sandbox will
  automatically archive. Default is 7 days.
  0 means the maximum interval will be used.
- `auto_delete_interval` _Optional[int]_ - Interval in minutes after which a continuously stopped Sandbox will
  automatically be deleted. By default, auto-delete is disabled.
  Negative value means disabled, 0 means delete immediately upon stopping.

## CreateSandboxFromImageParams

```python
class CreateSandboxFromImageParams(CreateSandboxBaseParams)
```

Parameters for creating a new Sandbox from an image.

**Attributes**:

- `image` _Union[str, Image]_ - Custom Docker image to use for the Sandbox. If an Image object is provided,
  the image will be dynamically built.
- `resources` _Optional[Resources]_ - Resource configuration for the Sandbox. If not provided, sandbox will
  have default resources.

## CreateSandboxFromSnapshotParams

```python
class CreateSandboxFromSnapshotParams(CreateSandboxBaseParams)
```

Parameters for creating a new Sandbox from a snapshot.

**Attributes**:

- `snapshot` _Optional[str]_ - Name of the snapshot to use for the Sandbox.

