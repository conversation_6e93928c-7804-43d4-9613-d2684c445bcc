## daytona sandbox list

List sandboxes

```
daytona sandbox list [flags]
```

### Options

```
  -f, --format string   Output format. Must be one of (yaml, json)
  -l, --limit int       Maximum number of items per page (default 100)
  -p, --page int        Page number for pagination (starting from 1) (default 1)
  -v, --verbose         Include verbose output
```

### Options inherited from parent commands

```
      --help   help for daytona
```

### SEE ALSO

- [daytona sandbox](daytona_sandbox.md) - Manage Daytona sandboxes
