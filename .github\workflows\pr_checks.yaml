name: '[PR] Validate code'

on:
  pull_request:
    branches:
      - main

permissions:
  contents: read

concurrency:
  # New commit on branch cancels running workflows of the same branch
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  go-work:
    name: Go work
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.work
          cache: false
      - name: go work
        run: |
          go work sync
          git diff --exit-code go.work || (echo "go.work is not up to date! Please run 'go work sync' and commit" && exit 1)
          git diff --exit-code go.work.sum || (echo "go.work.sum is not up to date! Please run 'go work sync' and commit" && exit 1)

  golangci:
    name: Go lint
    runs-on: ubuntu-latest
    strategy:
      matrix:
        working-directory: [apps/daemon, apps/runner, apps/cli, apps/proxy]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.work
          cache: false
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: ${{ matrix.working-directory }}
          args: --timeout=5m ./...
      - name: format
        run: |
          cd ${{ matrix.working-directory }}
          go fmt ./...
          git diff --exit-code '**/*.go' || (echo "Code is not formatted! Please run 'go fmt ./...' and commit" && exit 1)

  lint-computer-use:
    name: Go lint (Computer Use)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.work
          cache: false
      - name: format
        run: |
          sudo apt-get update && sudo apt-get install -y gcc libx11-dev libxtst-dev
          cd libs/computer-use
          go fmt ./...
          git diff --exit-code '**/*.go' || (echo "Code is not formatted! Please run 'go fmt ./...' and commit" && exit 1)
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: libs/computer-use
          args: --timeout=5m ./...

  format-lint-api-clients:
    name: Format, lint and generate API clients
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.work
          cache: true
      - uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: 'temurin'
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install "isort>=5.10.0,<6.0.0" "black[jupyter]>=23.1.0,<24.0.0" "nbqa>=1.9.1,<2.0.0 " "pylint>=3.3.4,<4.0.0" "unasync>=0.6.0,<0.7.0" "poetry==2.1.3"
      - run: corepack enable
      - name: generate-api-clients
        run: |
          go install github.com/swaggo/swag/cmd/swag@v1.16.4
          echo -e 'DEFAULT_PACKAGE_VERSION=0.0.0-dev\n\nPYPI_PKG_VERSION=\nNPM_PKG_VERSION=\nNPM_TAG=\nPYPI_TOKEN=\nNPM_TOKEN=' > .env
          yarn
          mkdir -p dist/apps/api
          yarn generate:openapi && yarn nx run-many --target=generate:api-client --all --parallel=false
          yarn sdk-python:generate-sync
          yarn lint:fix
          yarn format
          poetry lock
          git diff --exit-code || (echo "Code not formatted or linting errors! Hint: 'yarn generate:api-client', 'yarn sdk-python:generate-sync', 'yarn lint:fix' and commit" && exit 1)

  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.work
          cache: true
      - uses: actions/setup-node@v4
        with:
          node-version: 22
      - run: corepack enable
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Build all
        run: |
          python3 -m pip install poetry==2.1.3
          sudo apt-get update && sudo apt-get install -y gcc libx11-dev libxtst-dev
          yarn
          yarn build

  license-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Check AGPL License Headers
        uses: apache/skywalking-eyes/header@main
        with:
          token: ${{ github.token }}
          config: .licenserc.yaml
          mode: 'check'

      - name: Check Apache License Headers
        uses: apache/skywalking-eyes/header@main
        with:
          token: ${{ github.token }}
          config: .licenserc-clients.yaml
          mode: 'check'
