# Daytona Local Development and Integration Documentation

This documentation provides comprehensive guides for setting up Daytona locally and integrating it into your development projects. Whether you're looking to run Daytona on your local machine for development or integrate it into your existing projects, these guides will help you get started.

## 📚 Documentation Overview

This documentation collection includes three main guides:

### 1. [Local Docker Setup Guide](./LOCAL_DOCKER_SETUP.md)
Complete instructions for setting up and running Daytona locally using Docker. Perfect for:
- **Development and testing** of Daytona itself
- **Understanding** how Daytona works before integration
- **Local experimentation** with Daytona features
- **Contributing** to the Daytona project

### 2. [Environment Configuration Guide](./ENVIRONMENT_CONFIGURATION.md)
Detailed reference for environment variables, configuration files, and setup requirements. Essential for:
- **Configuring** Daytona services properly
- **Understanding** all available configuration options
- **Troubleshooting** configuration issues
- **Setting up** different environments (dev/staging/prod)

### 3. [Project Integration Guide](./PROJECT_INTEGRATION_GUIDE.md)
Comprehensive guide for integrating Daytona into your projects as a development environment tool. Includes:
- **SDK integration** examples (Python & TypeScript)
- **Configuration patterns** for different project types
- **Best practices** for team collaboration
- **CI/CD integration** examples

## 🚀 Quick Start

### For Local Development

If you want to run Daytona locally for development:

1. **Prerequisites**: Install Docker, Node.js, Go, and Python
2. **Clone**: `git clone https://github.com/daytonaio/daytona.git`
3. **Setup**: Follow the [Local Docker Setup Guide](./LOCAL_DOCKER_SETUP.md)
4. **Access**: Open http://localhost:3000 for the dashboard

### For Project Integration

If you want to integrate Daytona into your project:

1. **Install SDK**: `pip install daytona` or `npm install @daytonaio/sdk`
2. **Get API Key**: Create an account at https://app.daytona.io
3. **Configure**: Follow the [Project Integration Guide](./PROJECT_INTEGRATION_GUIDE.md)
4. **Start coding**: Create your first sandbox!

## 🛠️ What You'll Learn

### Local Setup
- How to set up a complete Daytona development environment
- Understanding the service architecture and dependencies
- Configuring databases, authentication, and storage
- Troubleshooting common setup issues
- Differences between local and hosted versions

### Environment Configuration
- Complete reference of all environment variables
- Service-specific configuration patterns
- Development vs production configuration
- Security best practices
- Configuration validation and debugging

### Project Integration
- Multiple integration patterns and approaches
- SDK usage examples for Python and TypeScript
- Project-specific configuration examples
- CI/CD integration strategies
- Best practices for team collaboration

## 🏗️ Architecture Overview

Daytona consists of several key components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Dashboard    │    │       API       │    │     Runner      │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Execution)   │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 3003    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Proxy       │    │   PostgreSQL    │    │     Docker      │
│  (Networking)   │    │   (Database)    │    │  (Containers)   │
│   Port: 4000    │    │   Port: 5432    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Supporting Services
- **Redis**: Caching and session storage
- **MinIO**: S3-compatible object storage
- **Dex**: OIDC authentication provider
- **Jaeger**: Distributed tracing
- **Docker Registry**: Container image storage

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: At least 20GB free space
- **Network**: Internet connection for downloading dependencies

### Required Software
- **Docker**: Version 24.0.7 or higher
- **Docker Compose**: Version 2.x
- **Node.js**: Version 22.14.0 or higher
- **Yarn**: Latest version
- **Go**: Version 1.23.5 or higher
- **Python**: Version 3.8 or higher
- **Poetry**: Version 2.1.3 or higher

## 🎯 Use Cases

### Local Development
- **Core Development**: Contributing to Daytona itself
- **Feature Testing**: Testing new features before deployment
- **Integration Testing**: Testing integrations with other services
- **Learning**: Understanding how Daytona works internally

### Project Integration
- **Development Environments**: Standardized dev environments for teams
- **CI/CD Pipelines**: Automated testing and deployment
- **AI Agent Integration**: Using Daytona with AI coding assistants
- **Container Orchestration**: Managing development containers

### Team Collaboration
- **Environment Standardization**: Consistent environments across team members
- **Onboarding**: Quick setup for new team members
- **Remote Development**: Cloud-based development environments
- **Resource Management**: Efficient use of development resources

## 🔧 Configuration Examples

### Basic Environment Setup
```bash
# .env.local
DAYTONA_API_KEY=your-api-key
DAYTONA_API_URL=https://api.daytona.io
DAYTONA_TARGET=us
NODE_ENV=development
```

### Python Integration
```python
from daytona import Daytona, Image

# Create development environment
image = Image.base("python:3.11").pip_install("fastapi", "uvicorn")
daytona = Daytona()
sandbox = daytona.create({"image": image})
```

### TypeScript Integration
```typescript
import { Daytona, Image } from '@daytonaio/sdk';

// Create development environment
const image = Image.base('node:20').runCommands('npm install -g typescript');
const daytona = new Daytona();
const sandbox = await daytona.create({ image });
```

## 🚨 Common Issues and Solutions

### Port Conflicts
If you encounter port conflicts, check what's running:
```bash
lsof -i :3000  # Dashboard
lsof -i :3001  # API
lsof -i :5432  # PostgreSQL
```

### Docker Issues
Reset Docker containers:
```bash
docker-compose down -v
docker-compose up -d
```

### Permission Issues
Ensure proper permissions for Docker:
```bash
sudo usermod -aG docker $USER
newgrp docker
```

## 📖 Additional Resources

### Official Documentation
- [Daytona Documentation](https://www.daytona.io/docs)
- [Python SDK Documentation](https://www.daytona.io/docs/python-sdk)
- [TypeScript SDK Documentation](https://www.daytona.io/docs/typescript-sdk)

### Community
- [Daytona Community Slack](https://go.daytona.io/slack)
- [GitHub Repository](https://github.com/daytonaio/daytona)
- [GitHub Issues](https://github.com/daytonaio/daytona/issues)

### Examples
- [Python Examples](https://github.com/daytonaio/daytona/tree/main/examples/python)
- [TypeScript Examples](https://github.com/daytonaio/daytona/tree/main/examples/typescript)
- [Jupyter Notebooks](https://github.com/daytonaio/daytona/tree/main/examples/jupyter)

## 🤝 Contributing

We welcome contributions to improve this documentation! Please:

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Test** your changes locally
5. **Submit** a pull request

### Documentation Guidelines
- Use clear, concise language
- Include practical examples
- Test all code examples
- Follow the existing structure and style
- Update the table of contents when adding sections

## 📄 License

This documentation is part of the Daytona project and is licensed under the [GNU AFFERO GENERAL PUBLIC LICENSE](../LICENSE).

---

**Need Help?** 
- Check the [troubleshooting sections](./LOCAL_DOCKER_SETUP.md#troubleshooting) in each guide
- Join our [community Slack](https://go.daytona.io/slack) for support
- Create an [issue](https://github.com/daytonaio/daytona/issues) for bugs or feature requests

**Ready to get started?** Choose your path:
- 🐳 [Set up Daytona locally](./LOCAL_DOCKER_SETUP.md) for development
- ⚙️ [Configure your environment](./ENVIRONMENT_CONFIGURATION.md) properly  
- 🔗 [Integrate Daytona](./PROJECT_INTEGRATION_GUIDE.md) into your project
