---
title: "Volume"
hideTitleOnPage: true
---

## Volume

```python
class Volume(VolumeDto)
```

Represents a Daytona Volume which is a shared storage volume for Sandboxes.

**Attributes**:

- `id` _StrictStr_ - Unique identifier for the Volume.
- `name` _StrictStr_ - Name of the Volume.
- `organization_id` _StrictStr_ - Organization ID of the Volume.
- `state` _StrictStr_ - State of the Volume.
- `created_at` _StrictStr_ - Date and time when the Volume was created.
- `updated_at` _StrictStr_ - Date and time when the Volume was last updated.
- `last_used_at` _StrictStr_ - Date and time when the Volume was last used.


## VolumeService

```python
class VolumeService()
```

Service for managing Daytona Volumes. Can be used to list, get, create and delete Volumes.

#### VolumeService.list

```python
def list() -> List[Volume]
```

List all Volumes.

**Returns**:

- `List[Volume]` - List of all Volumes.
  

**Example**:

```python
daytona = Daytona()
volumes = daytona.volume.list()
for volume in volumes:
    print(f"{volume.name} ({volume.id})")
```

#### VolumeService.get

```python
def get(name: str, create: bool = False) -> Volume
```

Get a Volume by name.

**Arguments**:

- `name` _str_ - Name of the Volume to get.
- `create` _bool_ - If True, create a new Volume if it doesn't exist.
  

**Returns**:

- `Volume` - The Volume object.
  

**Example**:

```python
daytona = Daytona()
volume = daytona.volume.get("test-volume-name", create=True)
print(f"{volume.name} ({volume.id})")
```

#### VolumeService.create

```python
def create(name: str) -> Volume
```

Create a new Volume.

**Arguments**:

- `name` _str_ - Name of the Volume to create.
  

**Returns**:

- `Volume` - The Volume object.
  

**Example**:

```python
daytona = Daytona()
volume = daytona.volume.create("test-volume")
print(f"{volume.name} ({volume.id}); state: {volume.state}")
```

#### VolumeService.delete

```python
def delete(volume: Volume) -> None
```

Delete a Volume.

**Arguments**:

- `volume` _Volume_ - Volume to delete.
  

**Example**:

```python
daytona = Daytona()
volume = daytona.volume.get("test-volume")
daytona.volume.delete(volume)
print("Volume deleted")
```

