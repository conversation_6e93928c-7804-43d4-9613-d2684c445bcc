---
title: "Snapshot"
hideTitleOnPage: true
---


## SnapshotService

Service for managing Daytona Snapshots. Can be used to list, get, create and delete Snapshots.

### Constructors

#### new SnapshotService()

```ts
new SnapshotService(snapshotsApi: SnapshotsApi, objectStorageApi: ObjectStorageApi): SnapshotService
```

**Parameters**:

- `snapshotsApi` _SnapshotsApi_
- `objectStorageApi` _ObjectStorageApi_


**Returns**:

- `SnapshotService`

### Methods

#### create()

```ts
create(params: CreateSnapshotParams, options: {
  onLogs: (chunk: string) => void;
  timeout: number;
}): Promise<Snapshot>
```

Creates and registers a new snapshot from the given Image definition.

**Parameters**:

- `params` _CreateSnapshotParams_ - Parameters for snapshot creation.
- `options` _Options for the create operation._
- `onLogs?` _\(chunk: string\) =\> void_ - This callback function handles snapshot creation logs.
- `timeout?` _number_ - Default is no timeout. Timeout in seconds (0 means no timeout).


**Returns**:

- `Promise<Snapshot>`

**Example:**

```ts
const image = Image.debianSlim('3.12').pipInstall('numpy');
await daytona.snapshot.create({ name: 'my-snapshot', image: image }, { onLogs: console.log });
```

***

#### delete()

```ts
delete(snapshot: Snapshot): Promise<void>
```

Deletes a Snapshot.

**Parameters**:

- `snapshot` _Snapshot_ - Snapshot to delete


**Returns**:

- `Promise<void>`

**Throws**:

If the Snapshot does not exist or cannot be deleted

**Example:**

```ts
const daytona = new Daytona();
const snapshot = await daytona.snapshot.get("snapshot-name");
await daytona.snapshot.delete(snapshot);
console.log("Snapshot deleted successfully");
```

***

#### get()

```ts
get(name: string): Promise<Snapshot>
```

Gets a Snapshot by its name.

**Parameters**:

- `name` _string_ - Name of the Snapshot to retrieve


**Returns**:

- `Promise<Snapshot>` - The requested Snapshot

**Throws**:

If the Snapshot does not exist or cannot be accessed

**Example:**

```ts
const daytona = new Daytona();
const snapshot = await daytona.snapshot.get("snapshot-name");
console.log(`Snapshot ${snapshot.name} is in state ${snapshot.state}`);
```

***

#### list()

```ts
list(): Promise<Snapshot[]>
```

List all Snapshots.

**Returns**:

- `Promise<Snapshot[]>` - List of all Snapshots accessible to the user

**Example:**

```ts
const daytona = new Daytona();
const snapshots = await daytona.snapshot.list();
console.log(`Found ${snapshots.length} snapshots`);
snapshots.forEach(snapshot => console.log(`${snapshot.name} (${snapshot.imageName})`));
```

***


## CreateSnapshotParams

```ts
type CreateSnapshotParams = {
  entrypoint: string[];
  image: string | Image;
  name: string;
  resources: Resources;
};
```

Parameters for creating a new snapshot.

**Type declaration**:

- `entrypoint?` _string\[\]_
- `image` _string \| Image_
- `name` _string_
- `resources?` _Resources_


## Snapshot

```ts
type Snapshot = SnapshotDto & {
  __brand: "Snapshot";
};
```

Represents a Daytona Snapshot which is a pre-configured sandbox.

**Type declaration**:

- `\_\_brand` _"Snapshot"_

