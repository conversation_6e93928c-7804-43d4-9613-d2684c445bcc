{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "command": "yarn serve",
      "name": "Debug",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "command": "yarn serve:skip-runner",
      "name": "Debug - Skip Runner",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "command": "yarn serve:skip-proxy",
      "name": "Debug - Skip Proxy",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "name": "Runner",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/apps/runner/cmd/runner",
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/apps/runner/.env",
      "output": "${workspaceFolder}/dist/apps/runner",
      "preLaunchTask": "debug-build-runner"
    },
    {
      "name": "Daemon",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/apps/daemon/cmd/daemon",
      "console": "integratedTerminal",
      "output": "${workspaceFolder}/dist/apps/daemon"
    },
    {
      "name": "Proxy",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/apps/proxy/cmd/proxy",
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/apps/proxy/.env",
      "output": "${workspaceFolder}/dist/apps/proxy"
    }
  ]
}
