---
title: Linked Accounts
---

Daytona supports the linking of user accounts from various identity providers. At the moment, the following providers are supported:

- Google
- GitHub

This allows you to use different providers for logging into your Daytona account.

:::tip

#### Unlock higher usage limits

Linking your GitHub account is one of the requirements to automatically upgrade to Tier 2.

:::

## How to link an account

To link an account, go to the [Linked Accounts](https://app.daytona.io/dashboard/user/linked-accounts) page in the Daytona dashboard and click on the "Link Account" button for the account provider you want to link.

## How to unlink an account

To unlink an account, go to the [Linked Accounts](https://app.daytona.io/dashboard/user/linked-accounts) page in the Daytona dashboard and click on the "Unlink" button for the account provider you want to unlink.

## Need More?

If you need support for other identity providers, reach out to [<EMAIL>](mailto:<EMAIL>).
