---
title: "ObjectStorage"
hideTitleOnPage: true
---


## ObjectStorage

ObjectStorage class for interacting with object storage services.

### Param

The configuration for the object storage service.

### Constructors

#### new ObjectStorage()

```ts
new ObjectStorage(config: ObjectStorageConfig): ObjectStorage
```

**Parameters**:

- `config` _ObjectStorageConfig_


**Returns**:

- `ObjectStorage`

### Methods

#### upload()

```ts
upload(
   path: string, 
   organizationId: string, 
archiveBasePath: string): Promise<string>
```

Upload a file or directory to object storage.

**Parameters**:

- `path` _string_ - The path to the file or directory to upload.
- `organizationId` _string_ - The organization ID to use for the upload.
- `archiveBasePath` _string_ - The base path to use for the archive.


**Returns**:

- `Promise<string>` - The hash of the uploaded file or directory.

***


## ObjectStorageConfig

Configuration for the ObjectStorage class.

**Properties**:

- `accessKeyId` _string_ - The access key ID for the object storage service.
- `bucketName?` _string_ - The name of the bucket to use.
- `endpointUrl` _string_ - The endpoint URL for the object storage service.
- `secretAccessKey` _string_ - The secret access key for the object storage service.
- `sessionToken?` _string_ - The session token for the object storage service. Used for temporary credentials.