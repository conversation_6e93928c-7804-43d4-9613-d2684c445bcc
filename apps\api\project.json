{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"outputPath": "dist/apps/api", "main": "apps/api/src/main.ts", "tsConfig": "apps/api/tsconfig.app.json", "generatePackageJson": true, "target": "node", "compiler": "tsc", "sourceMap": true, "webpackConfig": "apps/api/webpack.config.js", "assets": [{"input": "apps/api/src/assets", "glob": "**/*", "output": "./assets/"}]}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false}}}, "openapi": {"executor": "nx:run-commands", "options": {"command": "yarn ts-node apps/api/src/generate-openapi.ts -o dist/apps/api/openapi.json", "env": {"TS_NODE_PROJECT": "apps/api/tsconfig.app.json", "NODE_OPTIONS": "--require tsconfig-paths/register", "SKIP_CONNECTIONS": "true"}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "api:build", "runBuildTargetDependencies": false, "watch": true}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd {projectRoot} && prettier --write \"**/*.{ts,json,mjs}\" --config ../../.prettierrc"}}, "test": {"options": {"passWithNoTests": true}}, "docker-build": {"dependsOn": ["build"], "command": "docker build -f apps/api/Dockerfile . -t api"}, "migration:generate": {"executor": "nx:run-commands", "options": {"command": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ../../node_modules/typeorm/cli.js migration:generate -d ./src/data-source.ts ./src/migrations/migration"}}}}