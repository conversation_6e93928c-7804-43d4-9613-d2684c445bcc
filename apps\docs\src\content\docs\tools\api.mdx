---
title: API
description: A reference of supported operations using the Daytona API.
---

import Label from '@components/Label.astro'

## POST /api-keys

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** | API key created successfully. |

## GET /api-keys

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | API keys retrieved successfully. |
| **`500`** | Error fetching API keys. |


## GET /api-keys/current

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | API key retrieved successfully. |


## GET /api-keys/\{name\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`name`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | API key retrieved successfully. |

## DELETE /api-keys/\{name\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`name`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | API key deleted successfully. |


## GET /organizations/invitations

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of organization invitations |


## GET /organizations/invitations/count

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Count of organization invitations |


## POST /organizations/invitations/\{invitationId\}/accept

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`invitationId`** | path | true | undefined | Invitation ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Organization invitation accepted successfully |


## POST /organizations/invitations/\{invitationId\}/decline

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`invitationId`** | path | true | undefined | Invitation ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Organization invitation declined successfully |


## POST /organizations

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** | Organization created successfully |

## GET /organizations

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of organizations |


## GET /organizations/\{organizationId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Organization details |

## DELETE /organizations/\{organizationId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization deleted successfully |


## GET /organizations/\{organizationId\}/usage

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Current usage overview |


## PATCH /organizations/\{organizationId\}/quota

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Organization details |


## POST /organizations/\{organizationId\}/leave

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization left successfully |


## POST /organizations/\{organizationId\}/suspend

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization suspended successfully |


## POST /organizations/\{organizationId\}/unsuspend

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization unsuspended successfully |


## POST /organizations/\{organizationId\}/roles

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** | Organization role created successfully |

## GET /organizations/\{organizationId\}/roles

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of organization roles |


## PUT /organizations/\{organizationId\}/roles/\{roleId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`roleId`** | path | true | undefined | Role ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Role updated successfully |

## DELETE /organizations/\{organizationId\}/roles/\{roleId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`roleId`** | path | true | undefined | Role ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization role deleted successfully |


## GET /organizations/\{organizationId\}/users

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of organization members |


## POST /organizations/\{organizationId\}/users/\{userId\}/role

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`userId`** | path | true | undefined | User ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Role updated successfully |


## POST /organizations/\{organizationId\}/users/\{userId\}/assigned-roles

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`userId`** | path | true | undefined | User ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Assigned roles updated successfully |


## DELETE /organizations/\{organizationId\}/users/\{userId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`userId`** | path | true | undefined | User ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | User removed from organization successfully |


## POST /organizations/\{organizationId\}/invitations

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** | Organization invitation created successfully |

## GET /organizations/\{organizationId\}/invitations

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of pending organization invitations |


## PUT /organizations/\{organizationId\}/invitations/\{invitationId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`invitationId`** | path | true | undefined | Invitation ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Organization invitation updated successfully |


## POST /organizations/\{organizationId\}/invitations/\{invitationId\}/cancel

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`organizationId`** | path | true | undefined | Organization ID |
| **`invitationId`** | path | true | undefined | Invitation ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Organization invitation cancelled successfully |


## GET /users/me

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | User details |


## POST /users

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** |  |

## GET /users

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## POST /users/\{id\}/regenerate-key-pair

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`id`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** |  |


## GET /users/account-providers

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Available account providers |


## POST /users/linked-accounts

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Account linked successfully |


## DELETE /users/linked-accounts/\{provider\}/\{providerUserId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`provider`** | path | true | undefined | undefined |
| **`providerUserId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | Account unlinked successfully |


## GET /users/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`id`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | User details |


## GET /sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`verbose`** | query | false | undefined | Include verbose output |
| **`labels`** | query | false | undefined | JSON encoded labels to filter by |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of all sandboxes |

## POST /sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The sandbox has been successfully created. |


## GET /sandbox/\{sandboxId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`verbose`** | query | false | undefined | Include verbose output |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox details |

## DELETE /sandbox/\{sandboxId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`force`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox has been deleted |


## POST /sandbox/\{sandboxId\}/start

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox has been started |


## POST /sandbox/\{sandboxId\}/stop

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox has been stopped |


## PUT /sandbox/\{sandboxId\}/labels

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Labels have been successfully replaced |


## POST /sandbox/\{sandboxId\}/backup

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox backup has been initiated |


## POST /sandbox/\{sandboxId\}/public/\{isPublic\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`isPublic`** | path | true | undefined | Public status to set |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** |  |


## POST /sandbox/\{sandboxId\}/autostop/\{interval\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`interval`** | path | true | undefined | Auto-stop interval in minutes (0 to disable) |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Auto-stop interval has been set |


## POST /sandbox/\{sandboxId\}/autoarchive/\{interval\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`interval`** | path | true | undefined | Auto-archive interval in minutes (0 means the maximum interval will be used) |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Auto-archive interval has been set |


## POST /sandbox/\{sandboxId\}/archive

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox has been archived |


## GET /sandbox/\{sandboxId\}/ports/\{port\}/preview-url

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`port`** | path | true | undefined | Port number to get preview URL for |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Preview URL for the specified port |


## GET /sandbox/\{sandboxId\}/build-logs

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`follow`** | query | false | undefined | Whether to follow the logs stream |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Build logs stream |


## POST /runners

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** |  |

## GET /runners

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## PATCH /runners/\{id\}/scheduling

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`id`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## GET /runners/by-sandbox/\{sandboxId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Runner found |


## GET /runners/by-snapshot

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`internalName`** | query | true | undefined | Internal name of the snapshot |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Runners found for the snapshot |


## GET /toolbox/\{sandboxId\}/toolbox/project-dir

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Project directory retrieved successfully |


## GET /toolbox/\{sandboxId\}/toolbox/files

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | false | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Files listed successfully |

## DELETE /toolbox/\{sandboxId\}/toolbox/files
Delete file inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File deleted successfully |


## GET /toolbox/\{sandboxId\}/toolbox/files/download
Download file from sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File downloaded successfully |


## GET /toolbox/\{sandboxId\}/toolbox/files/find
Search for text/pattern inside sandbox files

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |
| **`pattern`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Search completed successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/folder
Create folder inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |
| **`mode`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Folder created successfully |


## GET /toolbox/\{sandboxId\}/toolbox/files/info
Get file info inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File info retrieved successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/move
Move file inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`source`** | query | true | undefined | undefined |
| **`destination`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File moved successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/permissions
Set file owner/group/permissions inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |
| **`owner`** | query | false | undefined | undefined |
| **`group`** | query | false | undefined | undefined |
| **`mode`** | query | false | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File permissions updated successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/replace
Replace text/pattern in multiple files inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Text replaced successfully |


## GET /toolbox/\{sandboxId\}/toolbox/files/search
Search for files inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |
| **`pattern`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Search completed successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/upload
Upload file inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | File uploaded successfully |


## POST /toolbox/\{sandboxId\}/toolbox/files/bulk-upload
Upload multiple files inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Files uploaded successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/add
Add files to git commit

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Files added to git successfully |


## GET /toolbox/\{sandboxId\}/toolbox/git/branches
Get branch list from git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Branch list retrieved successfully |

## POST /toolbox/\{sandboxId\}/toolbox/git/branches
Create branch on git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Branch created successfully |

## DELETE /toolbox/\{sandboxId\}/toolbox/git/branches
Delete branch on git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Branch deleted successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/clone
Clone git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Repository cloned successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/commit
Commit changes to git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Changes committed successfully |


## GET /toolbox/\{sandboxId\}/toolbox/git/history
Get commit history from git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Commit history retrieved successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/pull
Pull changes from remote

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Changes pulled successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/push
Push changes to remote

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Changes pushed successfully |


## POST /toolbox/\{sandboxId\}/toolbox/git/checkout
Checkout branch or commit in git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Branch checked out successfully |


## GET /toolbox/\{sandboxId\}/toolbox/git/status
Get status from git repository

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`path`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Git status retrieved successfully |


## POST /toolbox/\{sandboxId\}/toolbox/process/execute
Execute command synchronously inside sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Command executed successfully |


## GET /toolbox/\{sandboxId\}/toolbox/process/session
List all active sessions in the sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sessions retrieved successfully |

## POST /toolbox/\{sandboxId\}/toolbox/process/session
Create a new session in the sandbox

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## GET /toolbox/\{sandboxId\}/toolbox/process/session/\{sessionId\}
Get session by ID

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`sessionId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Session retrieved successfully |

## DELETE /toolbox/\{sandboxId\}/toolbox/process/session/\{sessionId\}
Delete a specific session

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`sessionId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Session deleted successfully |


## POST /toolbox/\{sandboxId\}/toolbox/process/session/\{sessionId\}/exec
Execute a command in a specific session

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`sessionId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Command executed successfully |
| **`202`** | Command accepted and is being processed |


## GET /toolbox/\{sandboxId\}/toolbox/process/session/\{sessionId\}/command/\{commandId\}
Get session command by ID

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`sessionId`** | path | true | undefined | undefined |
| **`commandId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Session command retrieved successfully |


## GET /toolbox/\{sandboxId\}/toolbox/process/session/\{sessionId\}/command/\{commandId\}/logs
Get logs for a specific command in a session

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`sessionId`** | path | true | undefined | undefined |
| **`commandId`** | path | true | undefined | undefined |
| **`follow`** | query | false | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Command log stream |


## POST /toolbox/\{sandboxId\}/toolbox/lsp/completions
The Completion request is sent from the client to the server to compute completion items at a given cursor position.

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## POST /toolbox/\{sandboxId\}/toolbox/lsp/did-close
The document close notification is sent from the client to the server when the document got closed in the client.

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## POST /toolbox/\{sandboxId\}/toolbox/lsp/did-open
The document open notification is sent from the client to the server to signal newly opened text documents.

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## GET /toolbox/\{sandboxId\}/toolbox/lsp/document-symbols
The document symbol request is sent from the client to the server.

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`languageId`** | query | true | undefined | undefined |
| **`pathToProject`** | query | true | undefined | undefined |
| **`uri`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## POST /toolbox/\{sandboxId\}/toolbox/lsp/start
Start Lsp server process inside sandbox project

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## POST /toolbox/\{sandboxId\}/toolbox/lsp/stop
Stop Lsp server process inside sandbox project

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## GET /toolbox/\{sandboxId\}/toolbox/lsp/workspace-symbols
The workspace symbol request is sent from the client to the server to list project-wide symbols matching the query string.

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | undefined |
| **`languageId`** | query | true | undefined | undefined |
| **`pathToProject`** | query | true | undefined | undefined |
| **`query`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | OK |


## POST /snapshots

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The snapshot has been successfully created. |
| **`400`** | Bad request - Snapshots with tag ":latest" are not allowed |

## GET /snapshots

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`limit`** | query | false | undefined | Number of items per page |
| **`page`** | query | false | undefined | Page number |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of all snapshots with pagination |


## GET /snapshots/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID or name |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The snapshot |
| **`404`** | Snapshot not found |

## DELETE /snapshots/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Snapshot has been deleted |


## PATCH /snapshots/\{id\}/toggle

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Snapshot state has been toggled |


## PATCH /snapshots/\{id\}/general

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Snapshot general status has been set |


## GET /snapshots/\{id\}/build-logs

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID |
| **`follow`** | query | false | undefined | Whether to follow the logs stream |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## POST /snapshots/\{id\}/activate

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | Snapshot ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The snapshot has been successfully activated. |
| **`400`** | Bad request - Snapshot is already active, not in inactive state, or has associated snapshot runners |
| **`404`** | Snapshot not found |


## GET /workspace

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`verbose`** | query | false | undefined | Include verbose output |
| **`labels`** | query | false | undefined | JSON encoded labels to filter by |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of all workspacees |

## POST /workspace

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The workspace has been successfully created. |


## GET /workspace/\{workspaceId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`verbose`** | query | false | undefined | Include verbose output |
| **`workspaceId`** | path | true | undefined | ID of the workspace |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace details |

## DELETE /workspace/\{workspaceId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`force`** | query | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace has been deleted |


## POST /workspace/\{workspaceId\}/start

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace has been started |


## POST /workspace/\{workspaceId\}/stop

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace has been stopped |


## PUT /workspace/\{workspaceId\}/labels

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Labels have been successfully replaced |


## POST /workspace/\{workspaceId\}/backup

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace backup has been initiated |


## POST /workspace/\{workspaceId\}/public/\{isPublic\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`isPublic`** | path | true | undefined | Public status to set |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** |  |


## POST /workspace/\{workspaceId\}/autostop/\{interval\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`interval`** | path | true | undefined | Auto-stop interval in minutes (0 to disable) |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Auto-stop interval has been set |


## POST /workspace/\{workspaceId\}/autoarchive/\{interval\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`interval`** | path | true | undefined | Auto-archive interval in minutes (0 means the maximum interval will be used) |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Auto-archive interval has been set |


## POST /sandbox/\{sandboxId\}/autodelete/\{interval\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`interval`** | path | true | undefined | Auto-delete interval in minutes (negative value means disabled, 0 means delete immediately upon stopping) |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Auto-delete interval has been set |


## POST /workspace/\{workspaceId\}/archive

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Workspace has been archived |


## GET /workspace/\{workspaceId\}/ports/\{port\}/preview-url

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`port`** | path | true | undefined | Port number to get preview URL for |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Preview URL for the specified port |


## GET /workspace/\{workspaceId\}/build-logs

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`workspaceId`** | path | true | undefined | ID of the workspace |
| **`follow`** | query | false | undefined | Whether to follow the logs stream |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Build logs stream |


## GET /preview/\{sandboxId\}/public

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Public status of the sandbox |


## GET /preview/\{sandboxId\}/validate/\{authToken\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`sandboxId`** | path | true | undefined | ID of the sandbox |
| **`authToken`** | path | true | undefined | Auth token of the sandbox |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Sandbox auth token validation status |


## GET /preview/\{sandboxId\}/access

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`sandboxId`** | path | true | undefined | undefined |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** |  |


## GET /volumes

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`includeDeleted`** | query | false | undefined | Include deleted volumes in the response |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of all volumes |

## POST /volumes

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The volume has been successfully created. |


## GET /volumes/\{volumeId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`volumeId`** | path | true | undefined | ID of the volume |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Volume details |

## DELETE /volumes/\{volumeId\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`volumeId`** | path | true | undefined | ID of the volume |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Volume has been marked for deletion |


## GET /volumes/by-name/\{name\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`name`** | path | true | undefined | Name of the volume |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Volume details |


## POST /docker-registry

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`201`** | The docker registry has been successfully created. |

## GET /docker-registry

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | List of all docker registries |


## GET /docker-registry/registry-push-access

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Temporary registry access has been generated |


## GET /docker-registry/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | ID of the docker registry |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The docker registry |

## PATCH /docker-registry/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | ID of the docker registry |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The docker registry has been successfully updated. |

## DELETE /docker-registry/\{id\}

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | ID of the docker registry |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`204`** | The docker registry has been successfully deleted. |


## POST /docker-registry/\{id\}/set-default

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |
| **`id`** | path | true | undefined | ID of the docker registry |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | The docker registry has been set as default. |


## GET /object-storage/push-access

### Parameters
| Name | Location | Required | Type | Description |
| :--- | :------- | :------- | :--- | :---------- |
| **`X-Daytona-Organization-ID`** | header | false | undefined | Use with JWT to specify the organization ID |

### Responses
| Status Code | Description |
| :-------- | :---------- |
| **`200`** | Temporary storage access has been generated |



