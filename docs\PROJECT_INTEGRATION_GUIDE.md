# Daytona Project Integration Guide

This guide provides comprehensive instructions for integrating Daytona into your projects as a development environment tool, including configuration examples, best practices, and integration patterns for different project types.

## Table of Contents

- [Overview](#overview)
- [Integration Patterns](#integration-patterns)
- [SDK Integration](#sdk-integration)
- [Configuration Examples](#configuration-examples)
- [Project Type Integrations](#project-type-integrations)
- [CI/CD Integration](#cicd-integration)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

Daytona can be integrated into projects in several ways:

1. **SDK Integration**: Use Python or TypeScript SDKs to programmatically manage sandboxes
2. **CLI Integration**: Use the Daytona CLI for command-line operations
3. **MCP Integration**: Integrate with AI agents using the Model Context Protocol
4. **Container Integration**: Use Daytona as a development container platform
5. **API Integration**: Direct REST API integration for custom solutions

### MCP Integration

Daytona supports Model Context Protocol (MCP) for AI agent integration:

```bash
# Install Daytona CLI
brew install daytonaio/cli/daytona  # macOS/Linux
# or
powershell -Command "irm https://get.daytona.io/windows | iex"  # Windows

# Authenticate
daytona login

# Initialize MCP with your AI agent
daytona mcp init claude    # For Claude Desktop
daytona mcp init cursor    # For Cursor
daytona mcp init windsurf  # For Windsurf

# Get configuration for other agents
daytona mcp config
```

This enables AI agents to:
- Create and manage Daytona sandboxes
- Execute commands in sandboxes
- Perform file operations
- Generate preview links for web applications

## Integration Patterns

### 1. Development Environment as Code

Define your development environment using Daytona's Image API:

```python
from daytona import Daytona, Image, CreateSandboxFromImageParams

# Define your development environment
dev_image = (
    Image.base("ubuntu:22.04")
    .run_commands(
        "apt-get update && apt-get install -y curl git",
        "curl -fsSL https://deb.nodesource.com/setup_20.x | bash -",
        "apt-get install -y nodejs",
        "npm install -g typescript ts-node"
    )
    .pip_install("requests", "fastapi", "uvicorn")
    .copy_files("./requirements.txt", "/app/requirements.txt")
    .run_commands("cd /app && pip install -r requirements.txt")
)

# Create sandbox with your environment
daytona = Daytona()
sandbox = daytona.create(CreateSandboxFromImageParams(image=dev_image))
```

### 2. Project-Specific Configuration

Create a `daytona.config.py` file in your project root:

```python
# daytona.config.py
from daytona import Image

def get_project_image():
    """Define the development environment for this project."""
    return (
        Image.base("python:3.11-slim")
        .run_commands(
            "apt-get update && apt-get install -y git curl build-essential",
            "curl -fsSL https://deb.nodesource.com/setup_20.x | bash -",
            "apt-get install -y nodejs"
        )
        .copy_files("./requirements.txt", "/workspace/requirements.txt")
        .run_commands("pip install -r /workspace/requirements.txt")
        .copy_files("./package.json", "/workspace/package.json")
        .run_commands("cd /workspace && npm install")
    )

def get_project_config():
    """Project-specific Daytona configuration."""
    return {
        "image": get_project_image(),
        "language": "python",
        "working_directory": "/workspace",
        "environment_variables": {
            "PYTHONPATH": "/workspace",
            "NODE_ENV": "development"
        }
    }
```

### 3. Workspace Automation

Create automated development workflows:

```python
# scripts/dev_setup.py
from daytona import Daytona
from daytona.config import get_project_config

def setup_development_environment():
    """Set up a complete development environment."""
    daytona = Daytona()
    config = get_project_config()
    
    # Create sandbox
    sandbox = daytona.create(config)
    
    # Clone project repository
    sandbox.git.clone(
        "https://github.com/your-org/your-project.git",
        "/workspace",
        "main"
    )
    
    # Install dependencies
    sandbox.process.execute("cd /workspace && pip install -e .")
    sandbox.process.execute("cd /workspace && npm install")
    
    # Run initial setup
    sandbox.process.execute("cd /workspace && python scripts/setup.py")
    
    # Start development server
    sandbox.process.execute("cd /workspace && python manage.py runserver", detach=True)
    
    print(f"Development environment ready: {sandbox.get_preview_url()}")
    return sandbox

if __name__ == "__main__":
    setup_development_environment()
```

## SDK Integration

### Python SDK Integration

#### Basic Integration

```python
# requirements.txt
daytona>=1.0.0

# main.py
import os
from daytona import Daytona, DaytonaConfig

# Initialize Daytona client
config = DaytonaConfig(
    api_key=os.getenv("DAYTONA_API_KEY"),
    api_url=os.getenv("DAYTONA_API_URL", "https://api.daytona.io"),
    target=os.getenv("DAYTONA_TARGET", "us")
)
daytona = Daytona(config)

# Create and use sandbox
sandbox = daytona.create()
result = sandbox.process.code_run("print('Hello from Daytona!')")
print(result.result)
```

#### Advanced Integration with Custom Images

```python
from daytona import Daytona, Image, CreateSandboxFromImageParams

class ProjectEnvironment:
    def __init__(self):
        self.daytona = Daytona()
        self.sandbox = None
    
    def create_environment(self, project_type="python"):
        """Create environment based on project type."""
        if project_type == "python":
            image = self._get_python_image()
        elif project_type == "node":
            image = self._get_node_image()
        elif project_type == "fullstack":
            image = self._get_fullstack_image()
        else:
            raise ValueError(f"Unsupported project type: {project_type}")
        
        self.sandbox = self.daytona.create(
            CreateSandboxFromImageParams(image=image)
        )
        return self.sandbox
    
    def _get_python_image(self):
        return (
            Image.base("python:3.11")
            .pip_install("fastapi", "uvicorn", "pytest", "black", "flake8")
            .run_commands("pip install --upgrade pip")
        )
    
    def _get_node_image(self):
        return (
            Image.base("node:20")
            .run_commands(
                "npm install -g typescript ts-node nodemon",
                "npm install -g @types/node"
            )
        )
    
    def _get_fullstack_image(self):
        return (
            Image.base("ubuntu:22.04")
            .run_commands(
                "apt-get update && apt-get install -y curl git python3 python3-pip",
                "curl -fsSL https://deb.nodesource.com/setup_20.x | bash -",
                "apt-get install -y nodejs"
            )
            .pip_install("fastapi", "uvicorn")
            .run_commands("npm install -g typescript ts-node")
        )
```

### TypeScript SDK Integration

#### Basic Integration

```typescript
// package.json
{
  "dependencies": {
    "@daytonaio/sdk": "^1.0.0"
  }
}

// src/daytona.ts
import { Daytona, DaytonaConfig } from '@daytonaio/sdk';

const config: DaytonaConfig = {
  apiKey: process.env.DAYTONA_API_KEY!,
  apiUrl: process.env.DAYTONA_API_URL || 'https://api.daytona.io',
  target: process.env.DAYTONA_TARGET || 'us'
};

export const daytona = new Daytona(config);

// Usage
async function createDevEnvironment() {
  const sandbox = await daytona.create();
  const result = await sandbox.process.codeRun('console.log("Hello from Daytona!")');
  console.log(result.result);
}
```

#### Advanced TypeScript Integration

```typescript
import { Daytona, Image, CreateSandboxFromImageParams } from '@daytonaio/sdk';

export class ProjectManager {
  private daytona: Daytona;

  constructor() {
    this.daytona = new Daytona();
  }

  async createReactEnvironment() {
    const image = Image.base('node:20')
      .runCommands(
        'npm install -g create-react-app typescript',
        'npm install -g @types/react @types/react-dom'
      );

    const sandbox = await this.daytona.create({
      image,
      language: 'typescript'
    });

    // Set up React project
    await sandbox.process.execute('npx create-react-app my-app --template typescript');
    await sandbox.process.execute('cd my-app && npm start', { detach: true });

    return sandbox;
  }

  async createNextJsEnvironment() {
    const image = Image.base('node:20')
      .runCommands('npm install -g create-next-app');

    const sandbox = await this.daytona.create({
      image,
      language: 'typescript'
    });

    await sandbox.process.execute('npx create-next-app@latest my-next-app --typescript --tailwind --eslint');
    await sandbox.process.execute('cd my-next-app && npm run dev', { detach: true });

    return sandbox;
  }
}
```

## Configuration Examples

### Environment Configuration File

Create a `.daytona.yaml` configuration file:

```yaml
# .daytona.yaml
version: "1.0"
name: "my-project"
description: "Development environment for My Project"

image:
  base: "ubuntu:22.04"
  commands:
    - "apt-get update && apt-get install -y curl git python3 python3-pip nodejs npm"
    - "pip3 install -r requirements.txt"
    - "npm install"
  
environment:
    PYTHONPATH: "/workspace"
    NODE_ENV: "development"
    DEBUG: "true"

ports:
  - 3000  # Frontend
  - 8000  # Backend API
  - 5432  # Database

volumes:
  - "./src:/workspace/src"
  - "./config:/workspace/config"

startup_commands:
  - "python manage.py migrate"
  - "npm run build"
  - "python manage.py runserver 0.0.0.0:8000 &"
  - "npm start"
```

### Docker Integration

```dockerfile
# Dockerfile.daytona
FROM daytonaio/sandbox:latest

# Install project dependencies
COPY requirements.txt /workspace/
RUN pip install -r /workspace/requirements.txt

COPY package.json package-lock.json /workspace/
RUN cd /workspace && npm install

# Copy project files
COPY . /workspace/

# Set working directory
WORKDIR /workspace

# Expose ports
EXPOSE 3000 8000

# Default command
CMD ["bash"]
```

### Docker Compose Integration

```yaml
# docker-compose.daytona.yml
version: '3.8'

services:
  daytona-dev:
    build:
      context: .
      dockerfile: Dockerfile.daytona
    ports:
      - "3000:3000"
      - "8000:8000"
    volumes:
      - .:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DAYTONA_API_KEY=${DAYTONA_API_KEY}
      - DAYTONA_API_URL=${DAYTONA_API_URL}
      - NODE_ENV=development
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: myproject
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"

  redis:
    image: redis:7
    ports:
      - "6379:6379"
```

## Project Type Integrations

### Python/Django Project

```python
# scripts/daytona_setup.py
from daytona import Daytona, Image, CreateSandboxFromImageParams

def setup_django_environment():
    image = (
        Image.base("python:3.11")
        .run_commands(
            "apt-get update && apt-get install -y postgresql-client",
            "pip install django djangorestframework psycopg2-binary"
        )
        .copy_files("./requirements.txt", "/app/requirements.txt")
        .run_commands("pip install -r /app/requirements.txt")
    )
    
    daytona = Daytona()
    sandbox = daytona.create(CreateSandboxFromImageParams(image=image))
    
    # Set up Django project
    sandbox.git.clone("https://github.com/your-org/django-project.git", "/app")
    sandbox.process.execute("cd /app && python manage.py migrate")
    sandbox.process.execute("cd /app && python manage.py collectstatic --noinput")
    sandbox.process.execute("cd /app && python manage.py runserver 0.0.0.0:8000", detach=True)
    
    return sandbox
```

### React/Node.js Project

```typescript
// scripts/setup-react-env.ts
import { Daytona, Image } from '@daytonaio/sdk';

async function setupReactEnvironment() {
  const image = Image.base('node:20')
    .runCommands(
      'npm install -g create-react-app typescript',
      'apt-get update && apt-get install -y git'
    );

  const daytona = new Daytona();
  const sandbox = await daytona.create({ image, language: 'typescript' });

  // Clone and setup project
  await sandbox.git.clone('https://github.com/your-org/react-project.git', '/app');
  await sandbox.process.execute('cd /app && npm install');
  await sandbox.process.execute('cd /app && npm run build');
  await sandbox.process.execute('cd /app && npm start', { detach: true });

  console.log(`React app running at: ${sandbox.getPreviewUrl()}`);
  return sandbox;
}
```

### Full-Stack Project

```python
# scripts/fullstack_setup.py
from daytona import Daytona, Image, CreateSandboxFromImageParams

def setup_fullstack_environment():
    image = (
        Image.base("ubuntu:22.04")
        .run_commands(
            "apt-get update && apt-get install -y curl git python3 python3-pip",
            "curl -fsSL https://deb.nodesource.com/setup_20.x | bash -",
            "apt-get install -y nodejs postgresql-client"
        )
        .pip_install("fastapi", "uvicorn", "sqlalchemy", "psycopg2-binary")
        .run_commands("npm install -g typescript ts-node")
    )
    
    daytona = Daytona()
    sandbox = daytona.create(CreateSandboxFromImageParams(image=image))
    
    # Setup backend
    sandbox.git.clone("https://github.com/your-org/backend.git", "/app/backend")
    sandbox.process.execute("cd /app/backend && pip install -r requirements.txt")
    sandbox.process.execute("cd /app/backend && uvicorn main:app --host 0.0.0.0 --port 8000", detach=True)
    
    # Setup frontend
    sandbox.git.clone("https://github.com/your-org/frontend.git", "/app/frontend")
    sandbox.process.execute("cd /app/frontend && npm install")
    sandbox.process.execute("cd /app/frontend && npm run build")
    sandbox.process.execute("cd /app/frontend && npm start", detach=True)
    
    return sandbox
```

## CI/CD Integration

### GitHub Actions

```yaml
# .github/workflows/daytona-test.yml
name: Test with Daytona

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install Daytona SDK
      run: pip install daytona
    
    - name: Run tests in Daytona
      env:
        DAYTONA_API_KEY: ${{ secrets.DAYTONA_API_KEY }}
        DAYTONA_API_URL: ${{ secrets.DAYTONA_API_URL }}
      run: |
        python scripts/run_tests_in_daytona.py
```

### GitLab CI

```yaml
# .gitlab-ci.yml
stages:
  - test
  - deploy

test_with_daytona:
  stage: test
  image: python:3.11
  before_script:
    - pip install daytona
  script:
    - python scripts/daytona_test.py
  variables:
    DAYTONA_API_KEY: $DAYTONA_API_KEY
    DAYTONA_API_URL: $DAYTONA_API_URL
```

## Best Practices

### 1. Environment Management

- **Use version control**: Store Daytona configurations in your repository
- **Environment isolation**: Use separate configurations for development, staging, and production
- **Reproducible environments**: Define environments as code using the Image API
- **Resource management**: Clean up unused sandboxes to manage costs

### 2. Security

- **API key management**: Store API keys securely using environment variables or secret management
- **Network security**: Use appropriate network configurations for your use case
- **Access control**: Implement proper authentication and authorization
- **Data protection**: Be mindful of sensitive data in sandbox environments

### 3. Performance

- **Image optimization**: Create efficient base images to reduce startup time
- **Resource allocation**: Right-size your sandbox resources
- **Caching**: Use image layers and dependency caching effectively
- **Monitoring**: Monitor sandbox performance and resource usage

### 4. Development Workflow

- **Automation**: Automate environment setup and teardown
- **Documentation**: Document your Daytona integration for team members
- **Testing**: Test your Daytona configurations regularly
- **Collaboration**: Share environment configurations across your team

## Troubleshooting

### Common Issues

#### Connection Problems
```python
# Test connection
from daytona import Daytona

try:
    daytona = Daytona()
    sandboxes = daytona.list()
    print("Connection successful")
except Exception as e:
    print(f"Connection failed: {e}")
```

#### Image Build Failures
```python
# Debug image building
from daytona import Image

try:
    image = Image.base("python:3.11").pip_install("requests")
    print("Image definition valid")
except Exception as e:
    print(f"Image build failed: {e}")
```

#### Resource Limitations
```python
# Check sandbox resources
sandbox = daytona.create()
status = sandbox.get_status()
print(f"CPU: {status.cpu_usage}%, Memory: {status.memory_usage}%")
```

### Debugging Tips

1. **Enable debug logging**: Set `LOG_LEVEL=debug` in your environment
2. **Check API responses**: Examine API response details for error information
3. **Validate configurations**: Ensure all required environment variables are set
4. **Test incrementally**: Build and test your environment step by step
5. **Monitor resources**: Keep an eye on CPU, memory, and storage usage

### Getting Help

- **Documentation**: Check the official Daytona documentation
- **Community**: Join the Daytona community Slack for support
- **GitHub Issues**: Report bugs and feature requests on GitHub
- **Examples**: Review the examples in the Daytona repository
