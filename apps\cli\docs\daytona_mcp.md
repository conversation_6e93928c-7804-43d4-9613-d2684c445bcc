## daytona mcp

Manage Daytona MCP Server

### Synopsis

Commands for managing Daytona MCP Server

### Options inherited from parent commands

```
      --help   help for daytona
```

### SEE ALSO

- [daytona](daytona.md) - Daytona CLI
- [daytona mcp config](daytona_mcp_config.md) - Outputs JSON configuration for Daytona MCP Server
- [daytona mcp init](daytona_mcp_init.md) - Initialize Daytona MCP Server with an agent (currently supported: claude, windsurf, cursor)
- [daytona mcp start](daytona_mcp_start.md) - Start Daytona MCP Server
