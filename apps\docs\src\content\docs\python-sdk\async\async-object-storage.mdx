---
title: "AsyncObjectStorage"
hideTitleOnPage: true
---

## AsyncObjectStorage

```python
class AsyncObjectStorage()
```

AsyncObjectStorage class for interacting with object storage services.

**Attributes**:

- `endpoint_url` _str_ - The endpoint URL for the object storage service.
- `aws_access_key_id` _str_ - The access key ID for the object storage service.
- `aws_secret_access_key` _str_ - The secret access key for the object storage service.
- `aws_session_token` _str_ - The session token for the object storage service. Used for temporary credentials.
- `bucket_name` _str_ - The name of the bucket to use.

#### AsyncObjectStorage.upload

```python
async def upload(path, organization_id, archive_base_path=None) -> str
```

Uploads a file to the object storage service.

**Arguments**:

- `path` _str_ - The path to the file to upload.
- `organization_id` _str_ - The organization ID to use.
- `archive_base_path` _str_ - The base path to use for the archive.


