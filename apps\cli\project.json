{"name": "cli", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/cli", "tags": [], "targets": {"build": {"executor": "@nx-go/nx-go:build", "options": {"main": "{projectRoot}/main.go", "outputPath": "dist/apps/cli"}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd {projectRoot} && go fmt ./... && prettier --write \"**/*.{yaml,html}\""}}, "test": {"executor": "@nx-go/nx-go:test"}, "lint": {"executor": "@nx-go/nx-go:lint"}}}