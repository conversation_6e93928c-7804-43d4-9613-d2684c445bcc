{"name": "Development tools", "id": "tools", "version": "1.0.0", "description": "Installs development tools.", "options": {"pipPackages": {"type": "array", "description": "List of pip packages to install", "items": {"type": "string"}}, "goTools": {"type": "array", "description": "List of Go tools to install", "items": {"type": "string"}}}, "installsAfter": ["ghcr.io/devcontainers/features/go", "ghcr.io/devcontainers/features/python", "ghcr.io/devcontainers/features/docker-in-docker"]}