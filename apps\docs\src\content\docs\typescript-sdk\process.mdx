---
title: "Process"
hideTitleOnPage: true
---


## CodeRunParams

Parameters for code execution.

**Properties**:

- `argv?` _string\[\]_ - Command line arguments
- `env?` _Record\<string, string\>_ - Environment variables
    




### Constructors

#### new CodeRunParams()

```ts
new CodeRunParams(): CodeRunParams
```

**Returns**:

- `CodeRunParams`
## Process

Handles process and code execution within a Sandbox.

### Constructors

#### new Process()

```ts
new Process(
   sandboxId: string, 
   codeToolbox: SandboxCodeToolbox, 
   toolboxApi: ToolboxApi, 
   getRootDir: () => Promise<string>): Process
```

**Parameters**:

- `sandboxId` _string_
- `codeToolbox` _SandboxCodeToolbox_
- `toolboxApi` _ToolboxApi_
- `getRootDir` _\(\) =\> Promise\<string\>_


**Returns**:

- `Process`

### Methods

#### codeRun()

```ts
codeRun(
   code: string, 
   params?: CodeRunParams, 
timeout?: number): Promise<ExecuteResponse>
```

Executes code in the Sandbox using the appropriate language runtime.

**Parameters**:

- `code` _string_ - Code to execute
- `params?` _CodeRunParams_ - Parameters for code execution
- `timeout?` _number_ - Maximum time in seconds to wait for execution to complete


**Returns**:

- `Promise<ExecuteResponse>` - Code execution results containing:
    - exitCode: The execution's exit status
    - result: Standard output from the code
    - artifacts: ExecutionArtifacts object containing `stdout` (same as result) and `charts` (matplotlib charts metadata)

**Examples:**

```ts
// Run TypeScript code
const response = await process.codeRun(`
  const x = 10;
  const y = 20;
  console.log(\`Sum: \${x + y}\`);
`);
console.log(response.artifacts.stdout);  // Prints: Sum: 30
```

```ts
// Run Python code with matplotlib
const response = await process.codeRun(`
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 30)
y = np.sin(x)

plt.figure(figsize=(8, 5))
plt.plot(x, y, 'b-', linewidth=2)
plt.title('Line Chart')
plt.xlabel('X-axis (seconds)')
plt.ylabel('Y-axis (amplitude)')
plt.grid(True)
plt.show()
`);

if (response.artifacts?.charts) {
  const chart = response.artifacts.charts[0];

  console.log(`Type: ${chart.type}`);
  console.log(`Title: ${chart.title}`);
  if (chart.type === ChartType.LINE) {
    const lineChart = chart as LineChart
    console.log('X Label:', lineChart.x_label)
    console.log('Y Label:', lineChart.y_label)
    console.log('X Ticks:', lineChart.x_ticks)
    console.log('Y Ticks:', lineChart.y_ticks)
    console.log('X Tick Labels:', lineChart.x_tick_labels)
    console.log('Y Tick Labels:', lineChart.y_tick_labels)
    console.log('X Scale:', lineChart.x_scale)
    console.log('Y Scale:', lineChart.y_scale)
    console.log('Elements:')
    console.dir(lineChart.elements, { depth: null })
  }
}
```

***

#### createSession()

```ts
createSession(sessionId: string): Promise<void>
```

Creates a new long-running background session in the Sandbox.

Sessions are background processes that maintain state between commands, making them ideal for
scenarios requiring multiple related commands or persistent environment setup. You can run
long-running commands and monitor process status.

**Parameters**:

- `sessionId` _string_ - Unique identifier for the new session


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Create a new session
const sessionId = 'my-session';
await process.createSession(sessionId);
const session = await process.getSession(sessionId);
// Do work...
await process.deleteSession(sessionId);
```

***

#### deleteSession()

```ts
deleteSession(sessionId: string): Promise<void>
```

Delete a session from the Sandbox.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session to delete


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Clean up a completed session
await process.deleteSession('my-session');
```

***

#### executeCommand()

```ts
executeCommand(
   command: string, 
   cwd?: string, 
   env?: Record<string, string>, 
timeout?: number): Promise<ExecuteResponse>
```

Executes a shell command in the Sandbox.

**Parameters**:

- `command` _string_ - Shell command to execute
- `cwd?` _string_ - Working directory for command execution. If not specified, uses the Sandbox root directory.
    Default is the user's root directory.
- `env?` _Record\<string, string\>_ - Environment variables to set for the command
- `timeout?` _number_ - Maximum time in seconds to wait for the command to complete. 0 means wait indefinitely.


**Returns**:

- `Promise<ExecuteResponse>` - Command execution results containing:
    - exitCode: The command's exit status
    - result: Standard output from the command
    - artifacts: ExecutionArtifacts object containing `stdout` (same as result) and `charts` (matplotlib charts metadata)

**Examples:**

```ts
// Simple command
const response = await process.executeCommand('echo "Hello"');
console.log(response.artifacts.stdout);  // Prints: Hello
```

```ts
// Command with working directory
const result = await process.executeCommand('ls', 'workspace/src');
```

```ts
// Command with timeout
const result = await process.executeCommand('sleep 10', undefined, 5);
```

***

#### executeSessionCommand()

```ts
executeSessionCommand(
   sessionId: string, 
   req: SessionExecuteRequest, 
timeout?: number): Promise<SessionExecuteResponse>
```

Executes a command in an existing session.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session to use
- `req` _SessionExecuteRequest_ - Command execution request containing:
    - command: The command to execute
    - runAsync: Whether to execute asynchronously
- `timeout?` _number_ - Timeout in seconds


**Returns**:

- `Promise<SessionExecuteResponse>` - Command execution results containing:
    - cmdId: Unique identifier for the executed command
    - output: Command output (if synchronous execution)
    - exitCode: Command exit status (if synchronous execution)

**Example:**

```ts
// Execute commands in sequence, maintaining state
const sessionId = 'my-session';

// Change directory
await process.executeSessionCommand(sessionId, {
  command: 'cd /home/<USER>'
});

// Run command in new directory
const result = await process.executeSessionCommand(sessionId, {
  command: 'pwd'
});
console.log(result.output);  // Prints: /home/<USER>
```

***

#### getSession()

```ts
getSession(sessionId: string): Promise<Session>
```

Get a session in the sandbox.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session to retrieve


**Returns**:

- `Promise<Session>` - Session information including:
    - sessionId: The session's unique identifier
    - commands: List of commands executed in the session

**Example:**

```ts
const session = await process.getSession('my-session');
session.commands.forEach(cmd => {
  console.log(`Command: ${cmd.command}`);
});
```

***

#### getSessionCommand()

```ts
getSessionCommand(sessionId: string, commandId: string): Promise<Command>
```

Gets information about a specific command executed in a session.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session
- `commandId` _string_ - Unique identifier of the command


**Returns**:

- `Promise<Command>` - Command information including:
    - id: The command's unique identifier
    - command: The executed command string
    - exitCode: Command's exit status (if completed)

**Example:**

```ts
const cmd = await process.getSessionCommand('my-session', 'cmd-123');
if (cmd.exitCode === 0) {
  console.log(`Command ${cmd.command} completed successfully`);
}
```

***

#### getSessionCommandLogs()

##### Call Signature

```ts
getSessionCommandLogs(sessionId: string, commandId: string): Promise<string>
```

Get the logs for a command executed in a session.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session
- `commandId` _string_ - Unique identifier of the command

**Returns**:

- `Promise<string>` - Command logs

**Example:**

```ts
const logs = await process.getSessionCommandLogs('my-session', 'cmd-123');
console.log('Command output:', logs);
```

##### Call Signature

```ts
getSessionCommandLogs(
   sessionId: string, 
   commandId: string, 
onLogs: (chunk: string) => void): Promise<void>
```

Asynchronously retrieve and process the logs for a command executed in a session as they become available.

**Parameters**:

- `sessionId` _string_ - Unique identifier of the session
- `commandId` _string_ - Unique identifier of the command
- `onLogs` _\(chunk: string\) =\> void_ - Callback function to handle each log chunk

**Returns**:

- `Promise<void>`

**Example:**

```ts
const logs = await process.getSessionCommandLogs('my-session', 'cmd-123', (chunk) => {
  console.log('Log chunk:', chunk);
});
```

***

#### listSessions()

```ts
listSessions(): Promise<Session[]>
```

Lists all active sessions in the Sandbox.

**Returns**:

- `Promise<Session[]>` - Array of active sessions

**Example:**

```ts
const sessions = await process.listSessions();
sessions.forEach(session => {
  console.log(`Session ${session.sessionId}:`);
  session.commands.forEach(cmd => {
    console.log(`- ${cmd.command} (${cmd.exitCode})`);
  });
});
```
