<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daytona</title>
  </head>
  <body>
    <div id="root"></div>
    <script>
      // Check for dark mode preference
      if (
        localStorage.theme === 'dark' ||
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
      ) {
        document.documentElement.classList.add('dark')
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      ;(function () {
        var e = window
        var t = document
        var n = function () {
          n.e(arguments)
        }
        n.q = []
        n.e = function (e) {
          n.q.push(e)
        }
        e.Pylon = n
        var r = function () {
          var e = t.createElement('script')
          e.setAttribute('type', 'text/javascript')
          e.setAttribute('async', 'true')
          e.setAttribute('src', 'https://widget.usepylon.com/widget/%VITE_PYLON_APP_ID%')
          var n = t.getElementsByTagName('script')[0]
          n.parentNode.insertBefore(e, n)
        }
        if (t.readyState === 'complete') {
          r()
        } else if (e.addEventListener) {
          e.addEventListener('load', r, false)
        }
      })()
    </script>
  </body>
</html>
