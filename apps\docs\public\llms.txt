# Daytona Documentation v0.0.0-dev
# Generated on: 2025-07-11

# Daytona

> Secure and Elastic Infrastructure for Running Your Al-Generated Code.

## Docs

- [API Keys](https://daytona.io/docs/api-keys)
- [Expiration](https://daytona.io/docs/api-keys#expiration)
- [Permissions](https://daytona.io/docs/api-keys#permissions)
- [Billing](https://daytona.io/docs/billing)
- [Wallet](https://daytona.io/docs/billing#wallet)
- [Automatic Top-Up](https://daytona.io/docs/billing#automatic-top-up)
- [Cost Breakdown](https://daytona.io/docs/billing#cost-breakdown)
- [Configuration](https://daytona.io/docs/configuration)
- [Set Up Your Environment Variables](https://daytona.io/docs/configuration#set-up-your-environment-variables)
- [Configuration Options](https://daytona.io/docs/configuration#configuration-options)
- [Environment Variables](https://daytona.io/docs/configuration#environment-variables)
- [Setting Environment Variables](https://daytona.io/docs/configuration#setting-environment-variables)
- [Configuration Precedence](https://daytona.io/docs/configuration#configuration-precedence)
- [Declarative Builder](https://daytona.io/docs/declarative-builder)
- [Overview](https://daytona.io/docs/declarative-builder#overview)
- [Base Image Selection](https://daytona.io/docs/declarative-builder#base-image-selection)
- [Package Management](https://daytona.io/docs/declarative-builder#package-management)
- [File System Operations](https://daytona.io/docs/declarative-builder#file-system-operations)
- [Environment Configuration](https://daytona.io/docs/declarative-builder#environment-configuration)
- [Dynamic Image Building](https://daytona.io/docs/declarative-builder#dynamic-image-building)
- [Creating Pre-built Snapshots](https://daytona.io/docs/declarative-builder#creating-pre-built-snapshots)
- [Using an Existing Dockerfile](https://daytona.io/docs/declarative-builder#using-an-existing-dockerfile)
- [Best Practices](https://daytona.io/docs/declarative-builder#best-practices)
- [File System Operations](https://daytona.io/docs/file-system-operations)
- [Basic Operations](https://daytona.io/docs/file-system-operations#basic-operations)
- [Listing Files and Directories](https://daytona.io/docs/file-system-operations#listing-files-and-directories)
- [Creating Directories](https://daytona.io/docs/file-system-operations#creating-directories)
- [Uploading Files](https://daytona.io/docs/file-system-operations#uploading-files)
- [Downloading Files](https://daytona.io/docs/file-system-operations#downloading-files)
- [Deleting files](https://daytona.io/docs/file-system-operations#deleting-files)
- [Advanced Operations](https://daytona.io/docs/file-system-operations#advanced-operations)
- [File Permissions](https://daytona.io/docs/file-system-operations#file-permissions)
- [File Search and Replace](https://daytona.io/docs/file-system-operations#file-search-and-replace)
- [Getting Started](https://daytona.io/docs/getting-started)
- [Install the Daytona SDK](https://daytona.io/docs/getting-started#install-the-daytona-sdk)
- [Run Code Inside a Sandbox](https://daytona.io/docs/getting-started#run-code-inside-a-sandbox)
- [Preview Your App](https://daytona.io/docs/getting-started#preview-your-app)
- [Connect to an LLM](https://daytona.io/docs/getting-started#connect-to-an-llm)
- [Additional Examples](https://daytona.io/docs/getting-started#additional-examples)
- [Setting up the Daytona CLI](https://daytona.io/docs/getting-started#setting-up-the-daytona-cli)
- [Git Operations](https://daytona.io/docs/git-operations)
- [Basic Operations](https://daytona.io/docs/git-operations#basic-operations)
- [Cloning Repositories](https://daytona.io/docs/git-operations#cloning-repositories)
- [Repository Status](https://daytona.io/docs/git-operations#repository-status)
- [Branch Operations](https://daytona.io/docs/git-operations#branch-operations)
- [Managing Branches](https://daytona.io/docs/git-operations#managing-branches)
- [Staging and Committing](https://daytona.io/docs/git-operations#staging-and-committing)
- [Working with Changes](https://daytona.io/docs/git-operations#working-with-changes)
- [Remote Operations](https://daytona.io/docs/git-operations#remote-operations)
- [Working with Remotes](https://daytona.io/docs/git-operations#working-with-remotes)
- [Daytona Documentation](https://daytona.io/docs/index)
- [Quick Start](https://daytona.io/docs/index#quick-start)
- [Language Server Protocol](https://daytona.io/docs/language-server-protocol)
- [Creating LSP Servers](https://daytona.io/docs/language-server-protocol#creating-lsp-servers)
- [Supported Languages](https://daytona.io/docs/language-server-protocol#supported-languages)
- [LSP Features](https://daytona.io/docs/language-server-protocol#lsp-features)
- [Code Completion](https://daytona.io/docs/language-server-protocol#code-completion)
- [Limits](https://daytona.io/docs/limits)
- [Manage usage dynamically](https://daytona.io/docs/limits#manage-usage-dynamically)
- [Tiers & Limit Increases](https://daytona.io/docs/limits#tiers--limit-increases)
- [Need More?](https://daytona.io/docs/limits#need-more)
- [Linked Accounts](https://daytona.io/docs/linked-accounts)
- [How to link an account](https://daytona.io/docs/linked-accounts#how-to-link-an-account)
- [How to unlink an account](https://daytona.io/docs/linked-accounts#how-to-unlink-an-account)
- [Need More?](https://daytona.io/docs/linked-accounts#need-more)
- [Log Streaming](https://daytona.io/docs/log-streaming)
- [Asynchronous](https://daytona.io/docs/log-streaming#asynchronous)
- [Synchronous](https://daytona.io/docs/log-streaming#synchronous)
- [Daytona MCP Server](https://daytona.io/docs/mcp)
- [Prerequisites](https://daytona.io/docs/mcp#prerequisites)
- [Installation and Setup](https://daytona.io/docs/mcp#installation-and-setup)
- [1. Install Daytona CLI](https://daytona.io/docs/mcp#1-install-daytona-cli)
- [2. Authenticate with Daytona](https://daytona.io/docs/mcp#2-authenticate-with-daytona)
- [3. Initialize MCP Server](https://daytona.io/docs/mcp#3-initialize-mcp-server)
- [4. Open Your AI Agent](https://daytona.io/docs/mcp#4-open-your-ai-agent)
- [Integration with Other AI Agents](https://daytona.io/docs/mcp#integration-with-other-ai-agents)
- [Available Tools](https://daytona.io/docs/mcp#available-tools)
- [Sandbox Management](https://daytona.io/docs/mcp#sandbox-management)
- [File Operations](https://daytona.io/docs/mcp#file-operations)
- [Preview](https://daytona.io/docs/mcp#preview)
- [Git Operations](https://daytona.io/docs/mcp#git-operations)
- [Command Execution](https://daytona.io/docs/mcp#command-execution)
- [Troubleshooting](https://daytona.io/docs/mcp#troubleshooting)
- [Support](https://daytona.io/docs/mcp#support)
- [Organizations](https://daytona.io/docs/organizations)
- [Organization Roles](https://daytona.io/docs/organizations#organization-roles)
- [Administrative Actions](https://daytona.io/docs/organizations#administrative-actions)
- [Inviting New Users](https://daytona.io/docs/organizations#inviting-new-users)
- [Available Assignments](https://daytona.io/docs/organizations#available-assignments)
- [Managing Invitations](https://daytona.io/docs/organizations#managing-invitations)
- [Settings](https://daytona.io/docs/organizations#settings)
- [Preview & Authentication](https://daytona.io/docs/preview-and-authentication)
- [Process and Code Execution](https://daytona.io/docs/process-code-execution)
- [Code Execution](https://daytona.io/docs/process-code-execution#code-execution)
- [Running Code](https://daytona.io/docs/process-code-execution#running-code)
- [Process Execution](https://daytona.io/docs/process-code-execution#process-execution)
- [Running Commands](https://daytona.io/docs/process-code-execution#running-commands)
- [Sessions (Background Processes)](https://daytona.io/docs/process-code-execution#sessions-background-processes)
- [Managing Long-Running Processes](https://daytona.io/docs/process-code-execution#managing-long-running-processes)
- [Best Practices](https://daytona.io/docs/process-code-execution#best-practices)
- [Common Issues](https://daytona.io/docs/process-code-execution#common-issues)
- [Process Execution Failed](https://daytona.io/docs/process-code-execution#process-execution-failed)
- [Process Timeout](https://daytona.io/docs/process-code-execution#process-timeout)
- [Resource Limits](https://daytona.io/docs/process-code-execution#resource-limits)
- [Region Selection](https://daytona.io/docs/regions)
- [Sandbox Management](https://daytona.io/docs/sandbox-management)
- [Sandbox Lifecycle](https://daytona.io/docs/sandbox-management#sandbox-lifecycle)
- [Creating Sandboxes](https://daytona.io/docs/sandbox-management#creating-sandboxes)
- [Basic Sandbox Creation](https://daytona.io/docs/sandbox-management#basic-sandbox-creation)
- [Sandbox Resources](https://daytona.io/docs/sandbox-management#sandbox-resources)
- [Sandbox Information](https://daytona.io/docs/sandbox-management#sandbox-information)
- [Stop and Start Sandbox](https://daytona.io/docs/sandbox-management#stop-and-start-sandbox)
- [Archive Sandbox](https://daytona.io/docs/sandbox-management#archive-sandbox)
- [Delete Sandbox](https://daytona.io/docs/sandbox-management#delete-sandbox)
- [Automated Lifecycle Management](https://daytona.io/docs/sandbox-management#automated-lifecycle-management)
- [Auto-stop Interval](https://daytona.io/docs/sandbox-management#auto-stop-interval)
- [Auto-archive Interval](https://daytona.io/docs/sandbox-management#auto-archive-interval)
- [Auto-delete Interval](https://daytona.io/docs/sandbox-management#auto-delete-interval)
- [Run Indefinitely](https://daytona.io/docs/sandbox-management#run-indefinitely)
- [Snapshots](https://daytona.io/docs/snapshots)
- [Creating Snapshots](https://daytona.io/docs/snapshots#creating-snapshots)
- [Snapshot Resources](https://daytona.io/docs/snapshots#snapshot-resources)
- [Images from Private Registries](https://daytona.io/docs/snapshots#images-from-private-registries)
- [Using a Local Image](https://daytona.io/docs/snapshots#using-a-local-image)
- [Deleting Snapshots](https://daytona.io/docs/snapshots#deleting-snapshots)
- [Default Snapshot](https://daytona.io/docs/snapshots#default-snapshot)
- [CLI](https://daytona.io/docs/tools/cli)
- [daytona](https://daytona.io/docs/tools/cli#daytona)
- [daytona autocomplete](https://daytona.io/docs/tools/cli#daytona-autocomplete)
- [daytona docs](https://daytona.io/docs/tools/cli#daytona-docs)
- [daytona login](https://daytona.io/docs/tools/cli#daytona-login)
- [daytona logout](https://daytona.io/docs/tools/cli#daytona-logout)
- [daytona mcp](https://daytona.io/docs/tools/cli#daytona-mcp)
- [daytona mcp config](https://daytona.io/docs/tools/cli#daytona-mcp-config)
- [daytona mcp init](https://daytona.io/docs/tools/cli#daytona-mcp-init)
- [daytona mcp start](https://daytona.io/docs/tools/cli#daytona-mcp-start)
- [daytona organization](https://daytona.io/docs/tools/cli#daytona-organization)
- [daytona organization create](https://daytona.io/docs/tools/cli#daytona-organization-create)
- [daytona organization delete](https://daytona.io/docs/tools/cli#daytona-organization-delete)
- [daytona organization list](https://daytona.io/docs/tools/cli#daytona-organization-list)
- [daytona organization use](https://daytona.io/docs/tools/cli#daytona-organization-use)
- [daytona sandbox](https://daytona.io/docs/tools/cli#daytona-sandbox)
- [daytona sandbox create](https://daytona.io/docs/tools/cli#daytona-sandbox-create)
- [daytona sandbox delete](https://daytona.io/docs/tools/cli#daytona-sandbox-delete)
- [daytona sandbox info](https://daytona.io/docs/tools/cli#daytona-sandbox-info)
- [daytona sandbox list](https://daytona.io/docs/tools/cli#daytona-sandbox-list)
- [daytona sandbox start](https://daytona.io/docs/tools/cli#daytona-sandbox-start)
- [daytona sandbox stop](https://daytona.io/docs/tools/cli#daytona-sandbox-stop)
- [daytona snapshot](https://daytona.io/docs/tools/cli#daytona-snapshot)
- [daytona snapshot create](https://daytona.io/docs/tools/cli#daytona-snapshot-create)
- [daytona snapshot delete](https://daytona.io/docs/tools/cli#daytona-snapshot-delete)
- [daytona snapshot list](https://daytona.io/docs/tools/cli#daytona-snapshot-list)
- [daytona snapshot push](https://daytona.io/docs/tools/cli#daytona-snapshot-push)
- [daytona version](https://daytona.io/docs/tools/cli#daytona-version)
- [daytona volume](https://daytona.io/docs/tools/cli#daytona-volume)
- [daytona volume create](https://daytona.io/docs/tools/cli#daytona-volume-create)
- [daytona volume delete](https://daytona.io/docs/tools/cli#daytona-volume-delete)
- [daytona volume get](https://daytona.io/docs/tools/cli#daytona-volume-get)
- [daytona volume list](https://daytona.io/docs/tools/cli#daytona-volume-list)
- [Volumes](https://daytona.io/docs/volumes)
- [Creating Volumes](https://daytona.io/docs/volumes#creating-volumes)
- [Mounting Volumes to Sandboxes](https://daytona.io/docs/volumes#mounting-volumes-to-sandboxes)
- [Deleting Volumes](https://daytona.io/docs/volumes#deleting-volumes)
- [Working with Volumes](https://daytona.io/docs/volumes#working-with-volumes)
- [Limitations](https://daytona.io/docs/volumes#limitations)
- [Web Terminal](https://daytona.io/docs/web-terminal)