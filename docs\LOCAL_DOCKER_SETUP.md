# Daytona Local Docker Setup Guide

This guide provides comprehensive instructions for setting up and running Daytona locally using Docker. This setup is ideal for development, testing, and understanding how Daytona works before integrating it into your projects.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Detailed Setup](#detailed-setup)
- [Environment Configuration](#environment-configuration)
- [Running the Services](#running-the-services)
- [Accessing the Services](#accessing-the-services)
- [Development Workflow](#development-workflow)
- [Troubleshooting](#troubleshooting)
- [Differences from Hosted Version](#differences-from-hosted-version)

## Prerequisites

Before setting up Daytona locally, ensure you have the following installed:

### Required Software

- **Docker**: Version 24.0.7 or higher
- **Docker Compose**: Version 2.x
- **Node.js**: Version 22.14.0 or higher
- **Yarn**: Latest version
- **Go**: Version 1.23.5 or higher
- **Python**: Version 3.8 or higher
- **Poetry**: Version 2.1.3 or higher

### System Requirements

- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: At least 20GB free space
- **OS**: Linux, macOS, or Windows with WSL2

### Installation Commands

#### macOS (using Homebrew)
```bash
# Install Docker Desktop
brew install --cask docker

# Install Node.js and Yarn
brew install node yarn

# Install Go
brew install go

# Install Python and Poetry
brew install python
curl -sSL https://install.python-poetry.org | python3 -
```

#### Ubuntu/Debian
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Node.js and Yarn
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g yarn

# Install Go
wget https://go.dev/dl/go1.23.5.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.23.5.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc

# Install Python and Poetry
sudo apt-get install -y python3 python3-pip
curl -sSL https://install.python-poetry.org | python3 -
```

#### Windows (using Chocolatey)
```powershell
# Install Docker Desktop
choco install docker-desktop

# Install Node.js and Yarn
choco install nodejs yarn

# Install Go
choco install golang

# Install Python and Poetry
choco install python
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -
```

## Quick Start

For a rapid setup using the development container:

1. **Clone the repository**:
   ```bash
   git clone https://github.com/daytonaio/daytona.git
   cd daytona
   ```

2. **Open in VS Code with Dev Containers**:
   ```bash
   code .
   # When prompted, click "Reopen in Container"
   ```

3. **Wait for setup to complete** - The dev container will automatically:
   - Install all dependencies
   - Set up the development environment
   - Start required services

4. **Access the services**:
   - Dashboard: http://localhost:3000
   - API: http://localhost:3001
   - Documentation: http://localhost:4321

## Detailed Setup

### 1. Clone and Setup Repository

```bash
# Clone the repository
git clone https://github.com/daytonaio/daytona.git
cd daytona

# Create environment file
touch .env.local

# Install dependencies
yarn install
poetry lock && poetry install
```

### 2. Build the Project

```bash
# Build all applications and libraries
yarn build

# Or build for production
yarn build:production
```

### 3. Set Up Docker Services

The project includes a comprehensive Docker Compose setup with all required services:

```bash
# Navigate to the dev container directory
cd .devcontainer

# Start all services
docker-compose up -d
```

This will start the following services:
- **PostgreSQL**: Database (port 5432)
- **Redis**: Caching and session storage (port 6379)
- **Dex**: OIDC provider for authentication (port 5556)
- **MinIO**: S3-compatible object storage (ports 9000, 9001)
- **Docker Registry**: Container registry (port 5000)
- **Registry UI**: Web interface for registry (port 5100)
- **PgAdmin**: Database administration (port 80)
- **MailDev**: Email testing (port 1080)
- **Jaeger**: Distributed tracing (port 16686)

## Environment Configuration

### Core Environment Variables

Create a `.env.local` file in the project root with the following variables:

```bash
# Development Environment
NODE_ENV=development
ENVIRONMENT=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=pass
DB_DATABASE=application_ctx

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# API Configuration
PORT=3001
APP_URL=http://localhost:3000

# Authentication (Dex OIDC)
OIDC_ISSUER=http://localhost:5556/dex
OIDC_CLIENT_ID=daytona
OIDC_CLIENT_SECRET=

# S3/MinIO Configuration
S3_ENDPOINT=http://localhost:9000
S3_REGION=us-east-1
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_DEFAULT_BUCKET=daytona

# Proxy Configuration
PROXY_DOMAIN=localtest.me
PROXY_PROTOCOL=http
PROXY_API_KEY=secret_api_token

# Runner Configuration
API_TOKEN=secret_api_token
CONTAINER_RUNTIME=docker
CONTAINER_NETWORK=bridge

# Development Features
NX_DAEMON=true
SKIP_CONNECTIONS=false
LOG_LEVEL=debug
```

### Service-Specific Configuration

#### API Service
```bash
# apps/api/.env.local
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=pass
DB_DATABASE=application_ctx
REDIS_HOST=localhost
REDIS_PORT=6379
```

#### Runner Service
```bash
# apps/runner/.env.local
API_TOKEN=secret_api_token
API_PORT=3003
CONTAINER_RUNTIME=docker
AWS_ENDPOINT_URL=http://localhost:9000
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_DEFAULT_BUCKET=daytona
```

#### Proxy Service
```bash
# apps/proxy/.env.local
PROXY_PORT=4000
PROXY_DOMAIN=localtest.me
PROXY_PROTOCOL=http
PROXY_API_KEY=secret_api_token
DAYTONA_API_URL=http://localhost:3001
```

## Running the Services

### Start All Services
```bash
# Start all services in development mode
yarn serve

# Start without runner (useful for API-only development)
yarn serve:skip-runner

# Start without proxy (useful for core development)
yarn serve:skip-proxy
```

### Start Individual Services

#### API Server
```bash
cd apps/api
yarn serve
# Runs on http://localhost:3001
```

#### Dashboard
```bash
cd apps/dashboard
yarn serve
# Runs on http://localhost:3000
```

#### Documentation
```bash
cd apps/docs
yarn dev
# Runs on http://localhost:4321
```

#### Runner
```bash
cd apps/runner
go run cmd/runner/main.go
# Runs on http://localhost:3003
```

#### Proxy
```bash
cd apps/proxy
go run cmd/proxy/main.go
# Runs on http://localhost:4000
```

## Accessing the Services

Once all services are running, you can access:

| Service | URL | Credentials |
|---------|-----|-------------|
| **Dashboard** | http://localhost:3000 | <EMAIL> / password |
| **API** | http://localhost:3001 | - |
| **Documentation** | http://localhost:4321 | - |
| **PgAdmin** | http://localhost:80 | <EMAIL> / pgadmin |
| **MinIO Console** | http://localhost:9001 | minioadmin / minioadmin |
| **Registry UI** | http://localhost:5100 | - |
| **MailDev** | http://localhost:1080 | - |
| **Jaeger** | http://localhost:16686 | - |

### Default Authentication

The development setup includes a default user:
- **Email**: <EMAIL>
- **Password**: password

## Development Workflow

### Making Changes

1. **Code Changes**: Edit files in your preferred editor
2. **Hot Reload**: Most services support hot reload for development
3. **Build**: Run `yarn build` if needed
4. **Test**: Run tests with `yarn test`
5. **Lint**: Check code quality with `yarn lint`

### Database Operations

```bash
# Generate migration
yarn migration:generate

# Run migrations
yarn migration:run

# Revert migration
yarn migration:revert
```

### API Client Generation

```bash
# Generate OpenAPI spec and clients
yarn generate:api-client
```

## Troubleshooting

### Common Issues

#### Port Conflicts
If you encounter port conflicts, check what's running:
```bash
# Check port usage
lsof -i :3000  # Dashboard
lsof -i :3001  # API
lsof -i :5432  # PostgreSQL
```

#### Docker Issues
```bash
# Reset Docker containers
docker-compose down -v
docker-compose up -d

# Clean Docker system
docker system prune -a
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose logs db

# Reset database
docker-compose down -v db
docker-compose up -d db
yarn migration:run
```

#### Node.js/Yarn Issues
```bash
# Clear node modules and reinstall
rm -rf node_modules yarn.lock
yarn install

# Clear Yarn cache
yarn cache clean
```

#### Go Module Issues
```bash
# Tidy Go modules
go mod tidy

# Clear Go module cache
go clean -modcache
```

### Performance Issues

#### Memory Usage
- Ensure you have at least 8GB RAM available
- Close unnecessary applications
- Consider increasing Docker memory limits

#### Build Performance
```bash
# Use parallel builds
yarn build --parallel

# Enable NX daemon for faster builds
export NX_DAEMON=true
```

### Debugging

#### Enable Debug Logging
```bash
# Set debug log level
export LOG_LEVEL=debug

# Enable verbose output
export DEBUG=*
```

#### VS Code Debugging
Use the provided launch configurations in `.vscode/launch.json`:
- **Debug**: Full stack
- **Debug - Skip Runner**: API and Dashboard only
- **Debug - Skip Proxy**: Core services only

## Differences from Hosted Version

### Local vs Hosted Daytona

| Aspect | Local Setup | Hosted Version |
|--------|-------------|----------------|
| **Infrastructure** | Your machine | Daytona Cloud |
| **Scaling** | Single instance | Auto-scaling |
| **Authentication** | Local Dex OIDC | Production OAuth |
| **Storage** | Local MinIO | Cloud storage |
| **Networking** | localhost | Production domains |
| **SSL/TLS** | HTTP only | HTTPS enforced |
| **Monitoring** | Basic logging | Full observability |
| **Backup** | Manual | Automated |

### Configuration Differences

#### Local Development
- Uses `localtest.me` for proxy domains
- HTTP-only connections
- Development authentication
- Local file storage
- Single-node setup

#### Production/Hosted
- Custom domains with SSL
- HTTPS everywhere
- Production OAuth providers
- Distributed storage
- Multi-region deployment

### Feature Limitations

Some features may not be available or work differently in local setup:
- **Auto-scaling**: Not available locally
- **Multi-region**: Single instance only
- **Production monitoring**: Limited to basic logging
- **Enterprise features**: May require additional configuration

### Migration Path

To move from local to hosted:
1. Export your configurations
2. Set up production environment variables
3. Configure production authentication
4. Migrate data if needed
5. Update DNS and SSL certificates

This local setup provides a complete development environment that closely mirrors the production system while being suitable for development, testing, and learning purposes.
