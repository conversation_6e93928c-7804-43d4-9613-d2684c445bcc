name: daytona sandbox create
synopsis: Create a new sandbox
usage: daytona sandbox create [flags]
options:
  - name: auto-archive
    default_value: '10080'
    usage: |
      Auto-archive interval in minutes (0 means the maximum interval will be used)
  - name: auto-delete
    default_value: '-1'
    usage: |
      Auto-delete interval in minutes (negative value means disabled, 0 means delete immediately upon stopping)
  - name: auto-stop
    default_value: '0'
    usage: Auto-stop interval in minutes (0 means disabled)
  - name: class
    usage: Sandbox class type (small, medium, large)
  - name: context
    shorthand: c
    default_value: '[]'
    usage: |
      Files or directories to include in the build context (can be specified multiple times)
  - name: cpu
    default_value: '0'
    usage: CPU cores allocated to the sandbox
  - name: disk
    default_value: '0'
    usage: Disk space allocated to the sandbox in GB
  - name: dockerfile
    shorthand: f
    usage: Path to Dockerfile for Sandbox snapshot
  - name: env
    shorthand: e
    default_value: '[]'
    usage: 'Environment variables (format: KEY=VALUE)'
  - name: gpu
    default_value: '0'
    usage: GPU units allocated to the sandbox
  - name: label
    shorthand: l
    default_value: '[]'
    usage: 'Labels (format: KEY=VALUE)'
  - name: memory
    default_value: '0'
    usage: Memory allocated to the sandbox in MB
  - name: public
    default_value: 'false'
    usage: Make sandbox publicly accessible
  - name: snapshot
    usage: Snapshot to use for the sandbox
  - name: target
    usage: Target region (eu, us)
  - name: user
    usage: User associated with the sandbox
  - name: volume
    shorthand: v
    default_value: '[]'
    usage: 'Volumes to mount (format: VOLUME_NAME:MOUNT_PATH)'
inherited_options:
  - name: help
    default_value: 'false'
    usage: help for daytona
see_also:
  - daytona sandbox - Manage Daytona sandboxes
