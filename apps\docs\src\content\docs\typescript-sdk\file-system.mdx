---
title: "FileSystem"
hideTitleOnPage: true
---


## FileSystem

Provides file system operations within a Sandbox.

### Constructors

#### new FileSystem()

```ts
new FileSystem(
   sandboxId: string, 
   toolboxApi: ToolboxApi, 
   getRootDir: () => Promise<string>): FileSystem
```

**Parameters**:

- `sandboxId` _string_
- `toolboxApi` _ToolboxApi_
- `getRootDir` _\(\) =\> Promise\<string\>_


**Returns**:

- `FileSystem`

### Methods

#### createFolder()

```ts
createFolder(path: string, mode: string): Promise<void>
```

Create a new directory in the Sandbox with specified permissions.

**Parameters**:

- `path` _string_ - Path where the directory should be created. Relative paths are resolved based on the user's
    root directory.
- `mode` _string_ - Directory permissions in octal format (e.g. "755")


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Create a directory with standard permissions
await fs.createFolder('app/data', '755');
```

***

#### deleteFile()

```ts
deleteFile(path: string): Promise<void>
```

Deletes a file or directory from the Sandbox.

**Parameters**:

- `path` _string_ - Path to the file or directory to delete. Relative paths are resolved based on the user's
    root directory.


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Delete a file
await fs.deleteFile('app/temp.log');
```

***

#### downloadFile()

##### Call Signature

```ts
downloadFile(remotePath: string, timeout?: number): Promise<Buffer<ArrayBufferLike>>
```

Downloads a file from the Sandbox. This method loads the entire file into memory, so it is not recommended
for downloading large files.

**Parameters**:

- `remotePath` _string_ - Path to the file to download. Relative paths are resolved based on the user's
    root directory.
- `timeout?` _number_ - Timeout for the download operation in seconds. 0 means no timeout.
    Default is 30 minutes.

**Returns**:

- `Promise<Buffer<ArrayBufferLike>>` - The file contents as a Buffer.

**Example:**

```ts
// Download and process a file
const fileBuffer = await fs.downloadFile('tmp/data.json');
console.log('File content:', fileBuffer.toString());
```

##### Call Signature

```ts
downloadFile(
   remotePath: string, 
   localPath: string, 
timeout?: number): Promise<void>
```

Downloads a file from the Sandbox and saves it to a local file. This method uses streaming to download the file,
so it is recommended for downloading larger files.

**Parameters**:

- `remotePath` _string_ - Path to the file to download in the Sandbox. Relative paths are resolved based on the user's
    root directory.
- `localPath` _string_ - Path to save the downloaded file.
- `timeout?` _number_ - Timeout for the download operation in seconds. 0 means no timeout.
    Default is 30 minutes.

**Returns**:

- `Promise<void>`

**Example:**

```ts
// Download and save a file
await fs.downloadFile('tmp/data.json', 'local_file.json');
```

***

#### findFiles()

```ts
findFiles(path: string, pattern: string): Promise<Match[]>
```

Searches for text patterns within files in the Sandbox.

**Parameters**:

- `path` _string_ - Directory to search in. Relative paths are resolved based on the user's
    root directory.
- `pattern` _string_ - Search pattern


**Returns**:

- `Promise<Match[]>` - Array of matches with file and line information

**Example:**

```ts
// Find all TODO comments in TypeScript files
const matches = await fs.findFiles('app/src', 'TODO:');
matches.forEach(match => {
  console.log(`${match.file}:${match.line}: ${match.content}`);
});
```

***

#### getFileDetails()

```ts
getFileDetails(path: string): Promise<FileInfo>
```

Retrieves detailed information about a file or directory.

**Parameters**:

- `path` _string_ - Path to the file or directory. Relative paths are resolved based on the user's
    root directory.


**Returns**:

- `Promise<FileInfo>` - Detailed file information including size, permissions, modification time

**Example:**

```ts
// Get file details
const info = await fs.getFileDetails('app/config.json');
console.log(`Size: ${info.size}, Modified: ${info.modTime}`);
```

***

#### listFiles()

```ts
listFiles(path: string): Promise<FileInfo[]>
```

Lists contents of a directory in the Sandbox.

**Parameters**:

- `path` _string_ - Directory path to list. Relative paths are resolved based on the user's
    root directory.


**Returns**:

- `Promise<FileInfo[]>` - Array of file and directory information

**Example:**

```ts
// List directory contents
const files = await fs.listFiles('app/src');
files.forEach(file => {
  console.log(`${file.name} (${file.size} bytes)`);
});
```

***

#### moveFiles()

```ts
moveFiles(source: string, destination: string): Promise<void>
```

Moves or renames a file or directory.

**Parameters**:

- `source` _string_ - Source path. Relative paths are resolved based on the user's
    root directory.
- `destination` _string_ - Destination path. Relative paths are resolved based on the user's
    root directory.


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Move a file to a new location
await fs.moveFiles('app/temp/data.json', 'app/data/data.json');
```

***

#### replaceInFiles()

```ts
replaceInFiles(
   files: string[], 
   pattern: string, 
newValue: string): Promise<ReplaceResult[]>
```

Replaces text content in multiple files.

**Parameters**:

- `files` _string\[\]_ - Array of file paths to process. Relative paths are resolved based on the user's
- `pattern` _string_ - Pattern to replace
- `newValue` _string_ - Replacement text


**Returns**:

- `Promise<ReplaceResult[]>` - Results of the replace operation for each file

**Example:**

```ts
// Update version number across multiple files
const results = await fs.replaceInFiles(
  ['app/package.json', 'app/version.ts'],
  '"version": "1.0.0"',
  '"version": "1.1.0"'
);
```

***

#### searchFiles()

```ts
searchFiles(path: string, pattern: string): Promise<SearchFilesResponse>
```

Searches for files and directories by name pattern in the Sandbox.

**Parameters**:

- `path` _string_ - Directory to search in. Relative paths are resolved based on the user's
- `pattern` _string_ - File name pattern (supports globs)


**Returns**:

- `Promise<SearchFilesResponse>` - Search results with matching files

**Example:**

```ts
// Find all TypeScript files
const result = await fs.searchFiles('app', '*.ts');
result.files.forEach(file => console.log(file));
```

***

#### setFilePermissions()

```ts
setFilePermissions(path: string, permissions: FilePermissionsParams): Promise<void>
```

Sets permissions and ownership for a file or directory.

**Parameters**:

- `path` _string_ - Path to the file or directory. Relative paths are resolved based on the user's
    root directory.
- `permissions` _FilePermissionsParams_ - Permission settings


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Set file permissions and ownership
await fs.setFilePermissions('app/script.sh', {
  owner: 'daytona',
  group: 'users',
  mode: '755'  // Execute permission for shell script
});
```

***

#### uploadFile()

##### Call Signature

```ts
uploadFile(
   file: Buffer, 
   remotePath: string, 
timeout?: number): Promise<void>
```

Uploads a file to the Sandbox. This method loads the entire file into memory, so it is not recommended
for uploading large files.

**Parameters**:

- `file` _Buffer_ - Buffer of the file to upload.
- `remotePath` _string_ - Destination path in the Sandbox. Relative paths are resolved based on the user's
    root directory.
- `timeout?` _number_ - Timeout for the upload operation in seconds. 0 means no timeout.
    Default is 30 minutes.

**Returns**:

- `Promise<void>`

**Example:**

```ts
// Upload a configuration file
await fs.uploadFile(Buffer.from('{"setting": "value"}'), 'tmp/config.json');
```

##### Call Signature

```ts
uploadFile(
   localPath: string, 
   remotePath: string, 
timeout?: number): Promise<void>
```

Uploads a file from the local file system to the Sandbox. This method uses streaming to upload the file,
so it is recommended for uploading larger files.

**Parameters**:

- `localPath` _string_ - Path to the local file to upload.
- `remotePath` _string_ - Destination path in the Sandbox. Relative paths are resolved based on the user's
    root directory.
- `timeout?` _number_ - Timeout for the upload operation in seconds. 0 means no timeout.
    Default is 30 minutes.

**Returns**:

- `Promise<void>`

**Example:**

```ts
// Upload a local file
await fs.uploadFile('local_file.txt', 'tmp/file.txt');
```

***

#### uploadFiles()

```ts
uploadFiles(files: FileUpload[], timeout?: number): Promise<void>
```

Uploads multiple files to the Sandbox. If files already exist at the destination paths,
they will be overwritten.

**Parameters**:

- `files` _FileUpload\[\]_ - Array of files to upload.
- `timeout?` _number = ..._ - Timeout for the upload operation in seconds. 0 means no timeout.
    Default is 30 minutes.


**Returns**:

- `Promise<void>`

**Example:**

```ts
// Upload multiple text files
const files = [
  {
    source: Buffer.from('Content of file 1'),
    destination: '/tmp/file1.txt'
  },
  {
    source: 'app/data/file2.txt',
    destination: '/tmp/file2.txt'
  },
  {
    source: Buffer.from('{"key": "value"}'),
    destination: '/tmp/config.json'
  }
];
await fs.uploadFiles(files);
```

***


## FilePermissionsParams

Parameters for setting file permissions in the Sandbox.

**Properties**:

- `group?` _string_ - Group owner of the file
- `mode?` _string_ - File mode/permissions in octal format (e.g. "644")
- `owner?` _string_ - User owner of the file
    




**Example:**

```ts
const permissions: FilePermissionsParams = {
  mode: '644',
  owner: 'daytona',
  group: 'users'
};
```
## FileUpload

Represents a file to be uploaded to the Sandbox.

**Properties**:

- `destination` _string_ - Absolute destination path in the Sandbox. Relative paths are resolved based on the user's
    root directory.
- `source` _string \| Buffer\<ArrayBufferLike\>_ - File to upload. If a Buffer, it is interpreted as the file content which is loaded into memory.
    Make sure it fits into memory, otherwise use the local file path which content will be streamed to the Sandbox.