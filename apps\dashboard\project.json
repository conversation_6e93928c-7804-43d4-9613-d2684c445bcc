{"name": "dashboard", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/dashboard/src", "projectType": "application", "tags": [], "targets": {"build": {"configurations": {"production": {"mode": "production"}}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd {projectRoot} && prettier --write \"**/*.{ts,tsx,js,jsx,json,css,mjs,html}\" --config ../../.prettierrc"}}}}