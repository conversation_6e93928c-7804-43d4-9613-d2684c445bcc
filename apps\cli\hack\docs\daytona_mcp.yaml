name: daytona mcp
synopsis: Manage Daytona MCP Server
description: Commands for managing Daytona MCP Server
inherited_options:
  - name: help
    default_value: 'false'
    usage: help for daytona
see_also:
  - daytona - Daytona CLI
  - daytona mcp config - Outputs JSON configuration for Daytona MCP Server
  - 'daytona mcp init - Initialize Daytona MCP Server with an agent (currently supported: claude, windsurf, cursor)'
  - daytona mcp start - Start Daytona MCP Server
