{"name": "daytona-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["api"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/daytona-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["api:build"]}, "format": {"executor": "nx:run-commands", "options": {"command": "cd {projectRoot} && prettier --write \"**/*.{ts,json,mjs}\" --config ../../.prettierrc"}}}}