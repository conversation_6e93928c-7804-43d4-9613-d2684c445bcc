version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    privileged: true
    volumes:
      - ..:/workspaces/daytona

  dex:
    image: dexidp/dex:v2.42.0
    volumes:
      - ./dex/config.yaml:/etc/dex/config.yaml
    command: ['dex', 'serve', '/etc/dex/config.yaml']
    network_mode: service:app

  db:
    image: postgres:11.9
    environment:
      - POSTGRES_PASSWORD=pass
      - POSTGRES_USER=user
      - POSTGRES_DB=application_ctx

  pgadmin:
    image: dpage/pgadmin4:9.2.0
    entrypoint: ['sh', '-c', 'chmod 600 /pgpass && exec /entrypoint.sh']
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pgadmin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    user: root
    volumes:
      - ./pgadmin4/servers.json:/pgadmin4/servers.json
      - ./pgadmin4/pgpass:/pgpass
    depends_on:
      - db

  redis:
    image: redis:latest

  registry-ui:
    image: joxit/docker-registry-ui:main
    restart: always
    environment:
      - SINGLE_REGISTRY=true
      - REGISTRY_TITLE=Docker Registry UI
      - DELETE_IMAGES=true
      - SHOW_CONTENT_DIGEST=true
      - NGINX_PROXY_PASS_URL=http://registry:5000
      - SHOW_CATALOG_NB_TAGS=true
      - CATALOG_MIN_BRANCHES=1
      - CATALOG_MAX_BRANCHES=1
      - TAGLIST_PAGE_SIZE=100
      - REGISTRY_SECURED=false
      - CATALOG_ELEMENTS_LIMIT=1000

  registry:
    image: registry:2.8.2
    restart: always
    environment:
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Origin: '[http://registry-ui.example.com]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Methods: '[HEAD,GET,OPTIONS,DELETE]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Credentials: '[true]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Headers: '[Authorization,Accept,Cache-Control]'
      REGISTRY_HTTP_HEADERS_Access-Control-Expose-Headers: '[Docker-Content-Digest]'
      REGISTRY_STORAGE_DELETE_ENABLED: 'true'
    volumes:
      - registry:/var/lib/registry

  maildev:
    image: maildev/maildev

  minio:
    image: minio/minio:latest
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_IDENTITY_STS_EXPIRY="24h"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"

  jaeger:
    image: jaegertracing/all-in-one:1.67.0

volumes:
  registry: {}
  minio_data: {}
