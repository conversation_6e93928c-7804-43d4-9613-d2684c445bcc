# Daytona Environment Configuration Guide

This guide provides detailed information about environment variables, configuration files, and setup requirements for Daytona development and deployment.

## Table of Contents

- [Overview](#overview)
- [Environment Variables Reference](#environment-variables-reference)
- [Configuration Files](#configuration-files)
- [Service-Specific Configuration](#service-specific-configuration)
- [Development vs Production](#development-vs-production)
- [Configuration Templates](#configuration-templates)
- [Best Practices](#best-practices)

## Overview

Daytona uses environment variables and configuration files to manage settings across different services and deployment environments. The configuration system supports:

- **Environment-based configuration**: Different settings for development, staging, and production
- **Service isolation**: Each service has its own configuration scope
- **Hierarchical loading**: Environment variables override configuration files
- **Validation**: Built-in validation for required and optional parameters

## Environment Variables Reference

### Core Application Variables

#### API Service Configuration
```bash
# Server Configuration
NODE_ENV=development|production
ENVIRONMENT=development|staging|production
PORT=3001                              # API server port
APP_URL=http://localhost:3000          # Frontend application URL

# Database Configuration
DB_HOST=localhost                      # PostgreSQL host
DB_PORT=5432                          # PostgreSQL port
DB_USERNAME=user                      # Database username
DB_PASSWORD=pass                      # Database password
DB_DATABASE=application_ctx           # Database name

# Redis Configuration
REDIS_HOST=localhost                  # Redis host
REDIS_PORT=6379                       # Redis port
REDIS_TLS=false                       # Enable TLS for Redis

# Authentication Configuration
OIDC_ISSUER=http://localhost:5556/dex # OIDC provider URL
OIDC_CLIENT_ID=daytona               # OIDC client ID
OIDC_CLIENT_SECRET=                  # OIDC client secret (optional for public clients)
OIDC_AUDIENCE=                       # OIDC audience (optional)

# S3/Object Storage Configuration
S3_ENDPOINT=http://localhost:9000     # S3-compatible endpoint
S3_STS_ENDPOINT=                     # STS endpoint (optional)
S3_REGION=us-east-1                  # S3 region
S3_ACCESS_KEY=minioadmin             # S3 access key
S3_SECRET_KEY=minioadmin             # S3 secret key
S3_DEFAULT_BUCKET=daytona            # Default bucket name
S3_ACCOUNT_ID=                       # AWS account ID (optional)
S3_ROLE_NAME=                        # IAM role name (optional)

# Feature Flags
SKIP_CONNECTIONS=false               # Skip external connections for testing
MAINTENANCE_MODE=false               # Enable maintenance mode
MAX_AUTO_ARCHIVE_INTERVAL=43200      # Auto-archive interval in seconds

# Monitoring and Analytics
POSTHOG_API_KEY=                     # PostHog analytics key
POSTHOG_HOST=                        # PostHog host
LOG_LEVEL=info                       # Logging level (debug, info, warn, error)
```

#### Runner Service Configuration
```bash
# Runner API Configuration
API_TOKEN=secret_api_token           # Required: API authentication token
API_PORT=8080                        # Runner API port
ENABLE_TLS=false                     # Enable TLS for runner API
TLS_CERT_FILE=                       # TLS certificate file path
TLS_KEY_FILE=                        # TLS private key file path

# Container Runtime Configuration
CONTAINER_RUNTIME=docker             # Container runtime (docker, podman)
CONTAINER_NETWORK=bridge             # Container network mode

# Storage Configuration
CACHE_RETENTION_DAYS=7               # Cache retention period
LOG_FILE_PATH=/tmp/runner.log        # Log file path

# AWS/S3 Configuration (for runner)
AWS_REGION=us-east-1                 # AWS region
AWS_ENDPOINT_URL=http://localhost:9000 # S3 endpoint URL
AWS_ACCESS_KEY_ID=minioadmin         # AWS access key
AWS_SECRET_ACCESS_KEY=minioadmin     # AWS secret key
AWS_DEFAULT_BUCKET=daytona           # Default S3 bucket
```

#### Proxy Service Configuration
```bash
# Proxy Server Configuration
PROXY_PORT=4000                      # Proxy server port
PROXY_DOMAIN=localtest.me            # Required: Proxy domain
PROXY_PROTOCOL=http                  # Required: Protocol (http, https)
PROXY_API_KEY=secret_api_token       # Required: API key for authentication

# TLS Configuration
ENABLE_TLS=false                     # Enable TLS for proxy
TLS_CERT_FILE=                       # TLS certificate file path
TLS_KEY_FILE=                        # TLS private key file path

# Backend Configuration
DAYTONA_API_URL=http://localhost:3001 # Required: Daytona API URL

# OIDC Configuration (for proxy)
OIDC_ISSUER=http://localhost:5556/dex # OIDC issuer URL
OIDC_CLIENT_ID=daytona               # OIDC client ID
OIDC_CLIENT_SECRET=                  # OIDC client secret

# Redis Configuration (for proxy sessions)
REDIS_HOST=localhost                 # Redis host
REDIS_PORT=6379                      # Redis port
REDIS_PASSWORD=                      # Redis password (optional)
```

#### Dashboard Configuration
```bash
# Frontend Configuration
VITE_API_URL=http://localhost:3001   # API base URL
VITE_OIDC_DOMAIN=localhost:5556      # OIDC domain
VITE_OIDC_CLIENT_ID=daytona         # OIDC client ID
VITE_OIDC_AUDIENCE=                 # OIDC audience

# Optional Features
VITE_BILLING_API_URL=               # Billing API URL
VITE_POSTHOG_KEY=                   # PostHog analytics key
VITE_POSTHOG_HOST=                  # PostHog host
VITE_LINKED_ACCOUNTS_ENABLED=false  # Enable linked accounts
VITE_ANNOUNCEMENT_BANNER_TEXT=      # Announcement banner text
VITE_ANNOUNCEMENT_BANNER_LEARN_MORE_URL= # Banner learn more URL
VITE_PYLON_APP_ID=                  # Pylon app ID
VITE_PROXY_TEMPLATE_URL=            # Proxy template URL
```

#### Daemon Configuration
```bash
# Daemon Configuration
DAYTONA_DAEMON_LOG_FILE_PATH=/tmp/daytona-daemon.log # Daemon log file path
```

### Development-Specific Variables

```bash
# NX Build System
NX_DAEMON=true                       # Enable NX daemon for faster builds
NX_CACHE_DIRECTORY=.nx/cache         # NX cache directory

# Development Tools
COREPACK_ENABLE_DOWNLOAD_PROMPT=0    # Disable corepack download prompt
POETRY_VIRTUALENVS_IN_PROJECT=true   # Create virtual environments in project

# Hot Reload and Development
CHOKIDAR_USEPOLLING=false           # Use polling for file watching
FAST_REFRESH=true                    # Enable React Fast Refresh
```

## Configuration Files

### Environment File Hierarchy

Daytona loads environment files in the following order (later files override earlier ones):

1. `.env` (base configuration)
2. `.env.local` (local overrides)
3. `.env.development` (development environment)
4. `.env.production` (production environment)
5. System environment variables (highest priority)

### Service Configuration Files

#### API Service (`apps/api/src/config/configuration.ts`)
```typescript
const configuration = {
  production: process.env.NODE_ENV === 'production',
  environment: process.env.ENVIRONMENT,
  port: parseInt(process.env.PORT, 10),
  appUrl: process.env.APP_URL,
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
  // ... additional configuration
}
```

#### Runner Configuration (`apps/runner/cmd/runner/config/config.go`)
```go
type Config struct {
    ApiToken           string `envconfig:"API_TOKEN" validate:"required"`
    ApiPort            int    `envconfig:"API_PORT"`
    TLSCertFile        string `envconfig:"TLS_CERT_FILE"`
    TLSKeyFile         string `envconfig:"TLS_KEY_FILE"`
    EnableTLS          bool   `envconfig:"ENABLE_TLS"`
    ContainerRuntime   string `envconfig:"CONTAINER_RUNTIME"`
    // ... additional fields
}
```

#### Proxy Configuration (`apps/proxy/cmd/proxy/config/config.go`)
```go
type Config struct {
    ProxyPort     int    `envconfig:"PROXY_PORT" validate:"required"`
    ProxyDomain   string `envconfig:"PROXY_DOMAIN" validate:"required"`
    ProxyProtocol string `envconfig:"PROXY_PROTOCOL" validate:"required"`
    ProxyApiKey   string `envconfig:"PROXY_API_KEY" validate:"required"`
    // ... additional fields
}
```

## Service-Specific Configuration

### Docker Compose Configuration

The development environment uses Docker Compose with the following services:

```yaml
# .devcontainer/docker-compose.yaml
services:
  db:
    image: postgres:11.9
    environment:
      - POSTGRES_PASSWORD=pass
      - POSTGRES_USER=user
      - POSTGRES_DB=application_ctx

  redis:
    image: redis:latest

  minio:
    image: minio/minio:latest
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"

  dex:
    image: dexidp/dex:v2.42.0
    volumes:
      - ./dex/config.yaml:/etc/dex/config.yaml
    command: ['dex', 'serve', '/etc/dex/config.yaml']
```

### OIDC Configuration (Dex)

```yaml
# .devcontainer/dex/config.yaml
issuer: http://localhost:5556/dex
storage:
  type: memory
web:
  http: 0.0.0.0:5556
  allowedOrigins: ['*']
staticClients:
  - id: daytona
    redirectURIs:
      - 'http://localhost:3000'
      - 'http://localhost:3000/api/oauth2-redirect.html'
    name: 'Daytona'
    public: true
enablePasswordDB: true
staticPasswords:
  - email: '<EMAIL>'
    hash: '$2a$10$2b2cU8CPhOTaGrs1HRQuAueS7JTT5ZHsHSzYiFPm1leZck7Mc8T4W'
    username: 'admin'
    userID: '1234'
```

## Development vs Production

### Development Configuration

```bash
# .env.local (development)
NODE_ENV=development
ENVIRONMENT=development
DB_HOST=localhost
REDIS_HOST=localhost
S3_ENDPOINT=http://localhost:9000
PROXY_DOMAIN=localtest.me
PROXY_PROTOCOL=http
SKIP_CONNECTIONS=false
LOG_LEVEL=debug
```

### Production Configuration

```bash
# .env.production
NODE_ENV=production
ENVIRONMENT=production
DB_HOST=your-db-host.com
REDIS_HOST=your-redis-host.com
S3_ENDPOINT=https://s3.amazonaws.com
PROXY_DOMAIN=your-domain.com
PROXY_PROTOCOL=https
SKIP_CONNECTIONS=false
LOG_LEVEL=info
ENABLE_TLS=true
```

## Configuration Templates

### Complete Development Template

```bash
# .env.local
# Core Configuration
NODE_ENV=development
ENVIRONMENT=development
PORT=3001
APP_URL=http://localhost:3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=user
DB_PASSWORD=pass
DB_DATABASE=application_ctx

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Authentication
OIDC_ISSUER=http://localhost:5556/dex
OIDC_CLIENT_ID=daytona

# Storage
S3_ENDPOINT=http://localhost:9000
S3_REGION=us-east-1
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_DEFAULT_BUCKET=daytona

# Proxy
PROXY_DOMAIN=localtest.me
PROXY_PROTOCOL=http
PROXY_API_KEY=secret_api_token

# Runner
API_TOKEN=secret_api_token
CONTAINER_RUNTIME=docker

# Development
NX_DAEMON=true
LOG_LEVEL=debug
```

### Production Template

```bash
# .env.production
# Core Configuration
NODE_ENV=production
ENVIRONMENT=production
PORT=3001
APP_URL=https://your-domain.com

# Database (use secure values)
DB_HOST=your-secure-db-host
DB_PORT=5432
DB_USERNAME=your-db-user
DB_PASSWORD=your-secure-password
DB_DATABASE=daytona_production

# Redis (use secure values)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_TLS=true

# Authentication (use production OIDC)
OIDC_ISSUER=https://your-oidc-provider.com
OIDC_CLIENT_ID=your-client-id
OIDC_CLIENT_SECRET=your-client-secret

# Storage (use production S3)
S3_ENDPOINT=https://s3.amazonaws.com
S3_REGION=us-east-1
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key
S3_DEFAULT_BUCKET=your-production-bucket

# Proxy (use production domain)
PROXY_DOMAIN=your-domain.com
PROXY_PROTOCOL=https
PROXY_API_KEY=your-secure-api-key
ENABLE_TLS=true
TLS_CERT_FILE=/path/to/cert.pem
TLS_KEY_FILE=/path/to/key.pem

# Runner
API_TOKEN=your-secure-runner-token
CONTAINER_RUNTIME=docker

# Production
LOG_LEVEL=info
MAINTENANCE_MODE=false
```

## Best Practices

### Security

1. **Never commit sensitive values**: Use `.env.local` for local development
2. **Use strong passwords**: Generate secure passwords for production
3. **Rotate secrets regularly**: Update API keys and passwords periodically
4. **Use TLS in production**: Always enable HTTPS for production deployments
5. **Validate configuration**: Use the built-in validation features

### Organization

1. **Group related variables**: Keep similar configuration together
2. **Use descriptive names**: Make variable names self-documenting
3. **Document required vs optional**: Clearly mark which variables are required
4. **Provide examples**: Include example values in documentation
5. **Use consistent naming**: Follow the established naming conventions

### Environment Management

1. **Separate environments**: Use different configuration for dev/staging/prod
2. **Use environment-specific files**: Leverage the file hierarchy
3. **Override with environment variables**: Use system env vars for deployment
4. **Validate on startup**: Ensure all required configuration is present
5. **Log configuration issues**: Provide clear error messages for misconfigurations

### Development Workflow

1. **Start with templates**: Use the provided configuration templates
2. **Test locally first**: Verify configuration in development environment
3. **Use development defaults**: Provide sensible defaults for development
4. **Document changes**: Update documentation when adding new configuration
5. **Version configuration**: Track configuration changes in version control
