## daytona

Daytona CLI

### Synopsis

Command line interface for Daytona Sandboxes

```
daytona [flags]
```

### Options

```
      --help      help for daytona
  -v, --version   Display the version of Daytona
```

### SEE ALSO

- [daytona autocomplete](daytona_autocomplete.md) - Adds a completion script for your shell environment
- [daytona docs](daytona_docs.md) - Opens the Daytona documentation in your default browser.
- [daytona login](daytona_login.md) - Log in to Daytona
- [daytona logout](daytona_logout.md) - Logout from Daytona
- [daytona mcp](daytona_mcp.md) - Manage Daytona MCP Server
- [daytona organization](daytona_organization.md) - Manage Daytona organizations
- [daytona sandbox](daytona_sandbox.md) - Manage Daytona sandboxes
- [daytona snapshot](daytona_snapshot.md) - Manage Daytona snapshots
- [daytona version](daytona_version.md) - Print the version number
- [daytona volume](daytona_volume.md) - Manage Daytona volumes
